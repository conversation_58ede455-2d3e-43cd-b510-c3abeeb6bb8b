"use client";

import { useState, useMemo, useEffect, useCallback } from "react";
import {
  ImportDataSheet,
  FieldMapping,
  FieldConfig,
} from "../data-import/import-data-sheet";
import {
  ImportVehicleReservationSchema,
  GetVehicleReservationsSchema,
} from "@/lib/vehicle-reservations/validations";
import { VehicleReservationStatus, PaymentStatus, VehiclePickupLocationType, ReservationSource } from "@/types/vehicle-reservation";
import { ApiStatus } from "@/types/common";
import {
  useVehicleReservationsSlim,
  useVehicleReservationsData,
  useVehicleReservationNumberAvailability,
  useBulkImportVehicleReservations,
} from "@/lib/vehicle-reservations/hooks";

// Define the vehicle reservation field types that can be imported
export type VehicleReservationImportFields =
  | "reservationNumber"
  | "referenceNumber"
  | "customerId"
  | "pickUpDate"
  | "returnDate"
  | "status"
  | "reservationSource"
  | "dailyRate"
  | "pickUpLocationType"
  | "pickUpAddress"
  | "returnLocationType"
  | "returnAddress"
  | "notes";

interface ImportVehicleReservationsSheetProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function ImportVehicleReservationsSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportVehicleReservationsSheetProps) {
  const [searchParams, setSearchParams] = useState<any>({});

  // Fetch existing vehicle reservations for validation
  const { data: vehicleReservationsResponse } = useVehicleReservationsData(
    searchParams,
    isDemo
  );
  const existingVehicleReservations = vehicleReservationsResponse?.data?.data || [];

  // Bulk import mutation
  const bulkImportMutation = useBulkImportVehicleReservations(isDemo);

  // Field configurations for vehicle reservation import
  const fieldConfigs: Record<VehicleReservationImportFields, FieldConfig> = useMemo(
    () => ({
      reservationNumber: {
        label: "Reservation Number",
        required: true,
        type: "text",
        description: "Unique reservation identifier",
        validation: {
          unique: true,
          validator: async (value: string) => {
            if (!value) return "Reservation number is required";
            
            // Check if reservation number already exists
            const exists = existingVehicleReservations.some(
              (reservation: any) => reservation.reservationNumber === value
            );
            if (exists) {
              return "Reservation number already exists";
            }
            return null;
          },
        },
      },
      referenceNumber: {
        label: "Reference Number",
        required: false,
        type: "text",
        description: "External reference number",
      },
      customerId: {
        label: "Customer ID",
        required: false,
        type: "text",
        description: "Customer identifier",
      },
      pickUpDate: {
        label: "Pick-up Date",
        required: true,
        type: "date",
        description: "Date when vehicle will be picked up (YYYY-MM-DD)",
      },
      returnDate: {
        label: "Return Date",
        required: true,
        type: "date",
        description: "Date when vehicle will be returned (YYYY-MM-DD)",
      },
      status: {
        label: "Status",
        required: false,
        type: "select",
        description: "Reservation status",
        options: Object.values(VehicleReservationStatus).map((status) => ({
          value: status,
          label: status.replace(/_/g, " "),
        })),
        defaultValue: VehicleReservationStatus.PENDING,
      },
      reservationSource: {
        label: "Reservation Source",
        required: false,
        type: "select",
        description: "How the reservation was made",
        options: Object.values(ReservationSource).map((source) => ({
          value: source,
          label: source.replace(/_/g, " "),
        })),
        defaultValue: ReservationSource.ONLINE,
      },
      dailyRate: {
        label: "Daily Rate",
        required: true,
        type: "number",
        description: "Daily rental rate in dollars",
        validation: {
          validator: (value: string) => {
            const num = parseFloat(value);
            if (isNaN(num) || num < 0) {
              return "Daily rate must be a positive number";
            }
            return null;
          },
        },
      },
      pickUpLocationType: {
        label: "Pick-up Location Type",
        required: true,
        type: "select",
        description: "Type of pick-up location",
        options: Object.values(VehiclePickupLocationType).map((type) => ({
          value: type,
          label: type.replace(/_/g, " "),
        })),
        defaultValue: VehiclePickupLocationType.OFFICE,
      },
      pickUpAddress: {
        label: "Pick-up Address",
        required: false,
        type: "text",
        description: "Pick-up location address",
      },
      returnLocationType: {
        label: "Return Location Type",
        required: true,
        type: "select",
        description: "Type of return location",
        options: Object.values(VehiclePickupLocationType).map((type) => ({
          value: type,
          label: type.replace(/_/g, " "),
        })),
        defaultValue: VehiclePickupLocationType.OFFICE,
      },
      returnAddress: {
        label: "Return Address",
        required: false,
        type: "text",
        description: "Return location address",
      },
      notes: {
        label: "Notes",
        required: false,
        type: "text",
        description: "Additional notes or comments",
      },
    }),
    [existingVehicleReservations]
  );

  // Handle successful import
  const handleImportSuccess = useCallback(
    (result: any) => {
      onSuccess?.();
      onOpenChange?.(false);
    },
    [onSuccess, onOpenChange]
  );

  // Handle import process
  const handleImport = useCallback(
    async (data: any[]) => {
      try {
        const result = await bulkImportMutation.mutateAsync({
          vehicleReservations: data,
        });

        if (result.status === ApiStatus.SUCCESS) {
          handleImportSuccess(result);
          return { success: true, data: result.data };
        } else {
          return {
            success: false,
            error: result.message || "Import failed",
          };
        }
      } catch (error: any) {
        console.error("Import error:", error);
        return {
          success: false,
          error: error.message || "Import failed",
        };
      }
    },
    [bulkImportMutation, handleImportSuccess]
  );

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Vehicle Reservations"
      description="Import vehicle reservations from a CSV file. Make sure your file includes the required fields."
      fieldConfigs={fieldConfigs}
      validationSchema={ImportVehicleReservationSchema}
      onImport={handleImport}
      isLoading={bulkImportMutation.isPending}
      templateFields={[
        "reservationNumber",
        "referenceNumber",
        "customerId",
        "pickUpDate",
        "returnDate",
        "status",
        "reservationSource",
        "dailyRate",
        "pickUpLocationType",
        "pickUpAddress",
        "returnLocationType",
        "returnAddress",
        "notes",
      ]}
      sampleData={[
        {
          reservationNumber: "RES-001",
          referenceNumber: "REF-001",
          customerId: "CUST-001",
          pickUpDate: "2024-01-15",
          returnDate: "2024-01-20",
          status: VehicleReservationStatus.PENDING,
          reservationSource: ReservationSource.ONLINE,
          dailyRate: "50.00",
          pickUpLocationType: VehiclePickupLocationType.OFFICE,
          pickUpAddress: "",
          returnLocationType: VehiclePickupLocationType.OFFICE,
          returnAddress: "",
          notes: "Standard rental",
        },
        {
          reservationNumber: "RES-002",
          referenceNumber: "REF-002",
          customerId: "CUST-002",
          pickUpDate: "2024-01-16",
          returnDate: "2024-01-18",
          status: VehicleReservationStatus.CONFIRMED,
          reservationSource: ReservationSource.PHONE,
          dailyRate: "75.00",
          pickUpLocationType: VehiclePickupLocationType.AIRPORT,
          pickUpAddress: "Terminal 1, Gate A",
          returnLocationType: VehiclePickupLocationType.AIRPORT,
          returnAddress: "Terminal 1, Gate A",
          notes: "Airport pickup required",
        },
      ]}
    />
  );
}
