"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { TrashIcon } from "@radix-ui/react-icons";
import { VehicleReservationTableData, VehicleReservationStatus } from "@/types/vehicle-reservation";
import { ApiStatus } from "@/types/common";
import { useBulkDeleteVehicleReservations } from "@/lib/vehicle-reservations/hooks";
import { toast } from "sonner";

interface DeleteVehicleReservationsDialogProps {
  vehicleReservations: VehicleReservationTableData[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteVehicleReservationsDialog({
  vehicleReservations,
  open,
  onOpenChange,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeleteVehicleReservationsDialogProps) {
  const bulkDeleteMutation = useBulkDeleteVehicleReservations(isDemo);

  // Check if any reservations are in active states that shouldn't be deleted
  const activeReservations = vehicleReservations.filter(
    (reservation) => 
      reservation.status === VehicleReservationStatus.CONFIRMED ||
      reservation.status === VehicleReservationStatus.PICKED_UP
  );

  const hasActiveReservations = activeReservations.length > 0;
  const isMultiple = vehicleReservations.length > 1;

  const handleDelete = async () => {
    if (vehicleReservations.length === 0 || hasActiveReservations) return;

    try {
      const vehicleReservationIds = vehicleReservations.map((reservation) => reservation.id);
      const result = await bulkDeleteMutation.mutateAsync(vehicleReservationIds);

      if (result.status === ApiStatus.SUCCESS && result.data) {
        toast.success(result.data.message);
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error(result.message || "Failed to delete vehicle reservations");
      }
    } catch (error) {
      console.error("Error deleting vehicle reservations:", error);
      toast.error("Failed to delete vehicle reservations");
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <AlertDialogTrigger asChild>
          <Button variant="destructive" size="sm">
            <TrashIcon className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {hasActiveReservations
              ? `Cannot Delete ${isMultiple ? "Vehicle Reservations" : "Vehicle Reservation"}`
              : "Are you sure?"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {hasActiveReservations ? (
              <>
                {isMultiple ? (
                  <>
                    The following vehicle reservations cannot be deleted because they are in active states:
                    <ul className="mt-2 list-disc list-inside">
                      {activeReservations.map((reservation) => (
                        <li key={reservation.id} className="font-medium">
                          {reservation.reservationNumber} ({reservation.status})
                        </li>
                      ))}
                    </ul>
                  </>
                ) : (
                  <>
                    The vehicle reservation{" "}
                    <span className="font-medium">
                      {activeReservations[0]?.reservationNumber}
                    </span>{" "}
                    cannot be deleted because it is in{" "}
                    <span className="font-medium">
                      {activeReservations[0]?.status}
                    </span>{" "}
                    status.
                  </>
                )}
                <span className="block mt-2 text-amber-600">
                  Please cancel or complete the reservation{isMultiple ? "s" : ""} first before deleting.
                </span>
              </>
            ) : (
              <>
                This will permanently delete{" "}
                {isMultiple
                  ? `${vehicleReservations.length} vehicle reservations`
                  : "the vehicle reservation"}{" "}
                {!isMultiple && (
                  <span className="font-medium">{vehicleReservations[0]?.reservationNumber}</span>
                )}
                . This action cannot be undone.
                {isMultiple && (
                  <div className="mt-2">
                    <span className="text-sm text-muted-foreground">
                      Reservations to be deleted:
                    </span>
                    <ul className="mt-1 list-disc list-inside text-sm">
                      {vehicleReservations.slice(0, 5).map((reservation) => (
                        <li key={reservation.id}>
                          {reservation.reservationNumber} ({reservation.status})
                        </li>
                      ))}
                      {vehicleReservations.length > 5 && (
                        <li className="text-muted-foreground">
                          ...and {vehicleReservations.length - 5} more
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            {hasActiveReservations ? "Close" : "Cancel"}
          </AlertDialogCancel>
          {!hasActiveReservations && (
            <AlertDialogAction
              onClick={handleDelete}
              disabled={bulkDeleteMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {bulkDeleteMutation.isPending
                ? "Deleting..."
                : `Delete ${
                    isMultiple ? `${vehicleReservations.length} Reservations` : "Reservation"
                  }`}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
