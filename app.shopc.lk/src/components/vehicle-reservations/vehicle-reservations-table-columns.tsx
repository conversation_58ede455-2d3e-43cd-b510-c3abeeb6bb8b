"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  FileText,
  Car,
  User,
} from "lucide-react";
import {
  VehicleReservationTableData,
  VehicleReservationStatus,
  PaymentStatus,
} from "@/types/vehicle-reservation";
import { VehicleReservationStatusBadge } from "./vehicle-reservation-status-badge";
import { Badge } from "@/components/ui/badge";
import { format, formatDistance } from "date-fns";
import { formatCurrency } from "@/lib/utils";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (
    vehicleReservationId: string,
    newStatus: VehicleReservationStatus
  ) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  onRefresh,
  onStatusUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<VehicleReservationTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "reservationNumber",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Reservation" />
      ),
      cell: ({ row }) => {
        const reservationNumber = row.getValue("reservationNumber") as string;
        const customerName = row.original.customerName;
        const referenceNumber = row.original.referenceNumber;

        return (
          <div className="flex items-center gap-3 pl-2">
            <div className="flex flex-col">
              <div className="font-medium">{reservationNumber}</div>
              {referenceNumber && (
                <div className="text-xs text-muted-foreground">
                  Ref: {referenceNumber}
                </div>
              )}
              {customerName && (
                <div className="text-xs text-muted-foreground">
                  {customerName}
                </div>
              )}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "pickUpDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Pickup / Return" />
      ),
      cell: ({ row }) => {
        const pickUpDate = row.original.pickUpDate;
        const returnDate = row.original.returnDate;
        return (
          <div>
            <div className="font-medium">
              {format(new Date(pickUpDate), "PP")}
            </div>
            <div className="text-sm text-muted-foreground">
              {format(new Date(returnDate), "PP")}
            </div>
            <div className="text-xs text-muted-foreground">
              {formatDistance(new Date(returnDate), new Date(pickUpDate), {
                addSuffix: false,
              })}
            </div>
          </div>
        );
      },
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      accessorKey: "vehicleCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Vehicles" />
      ),
      cell: ({ row }) => {
        const vehicleCount = row.original.vehicleCount;
        return (
          <div className="flex items-center gap-2">
            <Car className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{vehicleCount}</span>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "total",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Total" />
      ),
      cell: ({ row }) => {
        const total = parseFloat(row.original.total);
        const totalPaid = parseFloat(row.original.totalPaid);
        const outstandingBalance = parseFloat(row.original.outstandingBalance);

        return (
          <div className="flex flex-col">
            <div className="font-medium">{formatCurrency(total)}</div>
            {totalPaid > 0 && (
              <div className="text-xs text-green-600">
                Paid: {formatCurrency(totalPaid)}
              </div>
            )}
            {outstandingBalance > 0 && (
              <div className="text-xs text-amber-600">
                Due: {formatCurrency(outstandingBalance)}
              </div>
            )}
          </div>
        );
      },
      enableSorting: true,
      sortingFn: "basic",
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as VehicleReservationStatus;
        const vehicleReservationId = row.original.id;

        return (
          <VehicleReservationStatusBadge
            vehicleReservationId={vehicleReservationId}
            status={status}
            isDemo={isDemo}
            disabled={isActionsDisabled}
            onRefresh={onRefresh}
            onStatusUpdate={onStatusUpdate}
          />
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
      enableSorting: true,
    },
    {
      accessorKey: "paymentStatus",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Payment" />
      ),
      cell: ({ row }) => {
        const paymentStatus = row.original.paymentStatus;

        const getPaymentStatusVariant = (status: PaymentStatus) => {
          switch (status) {
            case PaymentStatus.PAID:
              return "default";
            case PaymentStatus.PARTIAL:
              return "secondary";
            case PaymentStatus.PENDING:
              return "outline";
            case PaymentStatus.REFUNDED:
              return "secondary";
            case PaymentStatus.CANCELLED:
              return "destructive";
            default:
              return "outline";
          }
        };

        return (
          <Badge variant={getPaymentStatusVariant(paymentStatus)}>
            {paymentStatus}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        return (
          <div className="text-sm">
            {format(new Date(row.original.createdAt), "PP")}
          </div>
        );
      },
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const vehicleReservation = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                disabled={isActionsDisabled}
                data-testid={`vehicle-reservation-actions-${vehicleReservation.id}`}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row: row,
                  })
                }
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
                <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "update",
                    row: row,
                  })
                }
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
                <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "delete",
                    row: row,
                  })
                }
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
                <DropdownMenuShortcut>⌘D</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableHiding: false,
    },
  ];

  return columns;
}
