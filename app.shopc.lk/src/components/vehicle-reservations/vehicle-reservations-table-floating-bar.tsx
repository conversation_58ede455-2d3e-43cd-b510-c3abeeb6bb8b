"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, Check<PERSON><PERSON>cle2, XC<PERSON>cle, Clock, Car, Ban } from "lucide-react";
import { toast } from "sonner";

import { type VehicleReservationTableData, VehicleReservationStatus } from "@/types/vehicle-reservation";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteVehicleReservations,
  useBulkUpdateVehicleReservationStatus,
} from "@/lib/vehicle-reservations/hooks";
import { ApiStatus } from "@/types/common";

interface VehicleReservationsTableFloatingBarProps {
  table: Table<VehicleReservationTableData>;
  onRefresh?: (vehicleReservationIds?: string[]) => void;
  isDemo?: boolean;
}

export function VehicleReservationsTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: VehicleReservationsTableFloatingBarProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "confirm" | "cancel" | "pickup" | "return"
  >();

  const bulkDeleteMutation = useBulkDeleteVehicleReservations(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateVehicleReservationStatus(isDemo);

  const isPending = bulkUpdateStatusMutation.isPending;

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedVehicleReservations = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedVehicleReservations.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedVehicleReservations.length, selectionCount]);

  const handleBulkStatusUpdate = React.useCallback(
    async (status: VehicleReservationStatus) => {
      if (selectionCount === 0) return;

      const actionMap = {
        [VehicleReservationStatus.CONFIRMED]: "confirm",
        [VehicleReservationStatus.CANCELLED]: "cancel",
        [VehicleReservationStatus.PICKED_UP]: "pickup",
        [VehicleReservationStatus.RETURNED]: "return",
      };

      setAction(actionMap[status] as any);

      try {
        const vehicleReservationIds = selectedVehicleReservations.map((reservation) => reservation.id);
        const result = await bulkUpdateStatusMutation.mutateAsync({
          vehicleReservationIds,
          status,
        });

        const failedUpdates = result.filter(
          (res: any) => res.status !== ApiStatus.SUCCESS
        );

        if (failedUpdates.length === 0) {
          const statusLabel = {
            [VehicleReservationStatus.CONFIRMED]: "confirmed",
            [VehicleReservationStatus.CANCELLED]: "cancelled",
            [VehicleReservationStatus.PICKED_UP]: "marked as picked up",
            [VehicleReservationStatus.RETURNED]: "marked as returned",
          }[status];

          toast.success(
            `Successfully ${statusLabel} ${selectionCount} vehicle reservation${selectionCount > 1 ? "s" : ""}`
          );
          table.toggleAllRowsSelected(false);
          onRefresh?.(vehicleReservationIds);
        } else {
          toast.error(
            `Failed to update ${failedUpdates.length} vehicle reservation${
              failedUpdates.length > 1 ? "s" : ""
            }`
          );
        }
      } catch (error) {
        console.error("Error updating vehicle reservations:", error);
        toast.error("Failed to update vehicle reservations");
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedVehicleReservations,
      selectionCount,
      bulkUpdateStatusMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No vehicle reservations selected" };

    try {
      const vehicleReservationIds = selectedVehicleReservations.map((reservation) => reservation.id);
      const result = await bulkDeleteMutation.mutateAsync(vehicleReservationIds);

      if (result.status === ApiStatus.SUCCESS) {
        return {}; // Success
      } else {
        return {
          error: result.message || "Failed to delete vehicle reservations",
        };
      }
    } catch (error: any) {
      console.error("Error deleting vehicle reservations:", error);
      return { error: error.message || "Failed to delete vehicle reservations" };
    }
  }, [selectedVehicleReservations, selectionCount, bulkDeleteMutation]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(VehicleReservationStatus.CONFIRMED)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Confirm selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(VehicleReservationStatus.PICKED_UP)}
            disabled={isPending || selectionCount === 0}
          >
            <Car className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Mark selected as picked up</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(VehicleReservationStatus.RETURNED)}
            disabled={isPending || selectionCount === 0}
          >
            <Clock className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Mark selected as returned</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(VehicleReservationStatus.CANCELLED)}
            disabled={isPending || selectionCount === 0}
          >
            <Ban className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Cancel selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<VehicleReservationTableData>
        table={table}
        title="Vehicle Reservations"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} vehicle reservation${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass IDs since they're deleted
        }}
      />
    </>
  );
}
