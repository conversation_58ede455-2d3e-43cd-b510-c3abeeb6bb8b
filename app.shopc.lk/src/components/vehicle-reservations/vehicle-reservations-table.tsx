"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
  Option,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { DeleteVehicleReservationsDialog } from "./delete-vehicle-reservations-dialog";
import { VehicleReservationsTableToolbarActions } from "./vehicle-reservations-table-toolbar-actions";
import { VehicleReservationsTableFloatingBar } from "./vehicle-reservations-table-floating-bar";
import { VehicleReservationSheet } from "./vehicle-reservation-sheet";
import { VehicleReservationDetails } from "./vehicle-reservation-details";
import { VehicleReservationDetailsContent } from "./vehicle-reservation-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./vehicle-reservations-table-columns";
import {
  VehicleReservationTableData,
  VehicleReservationStatus,
} from "@/types/vehicle-reservation";
import { useVehicleReservationsData } from "@/lib/vehicle-reservations/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { vehicleReservationKeys } from "@/lib/vehicle-reservations/hooks";
import { updateVehicleReservationPositions } from "@/lib/vehicle-reservations/queries";
import { ApiStatus } from "@/types/common";

interface VehicleReservationsTableProps {
  isDemo?: boolean;
}

export function VehicleReservationsTable({
  isDemo = false,
}: VehicleReservationsTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<VehicleReservationTableData> | null>(
      null
    );
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  string | (null > null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // Get perPage from URL params (use default if not set)
  const perPage = useMemo(() => {
    return searchParams.get("perPage")
      ? parseInt(searchParams.get("perPage") as string)
      : 10;
  }, [searchParams]);

  // For table view only - get current page (default to 1)
  const tablePage = useMemo(() => {
    return searchParams.get("page")
      ? parseInt(searchParams.get("page") as string)
      : 1;
  }, [searchParams]);

  // Build status options for filter
  const statusOptions = useMemo<Option[]>(() => {
    return Object.values(ReservationStatus).map((status) => ({
      label: status.replace("_", " "),
      value: status,
    }));
  }, []);

  // Build vehicle class options for filter
  const vehicleClassOptions = useMemo<Option[]>(() => {
    return Object.values(VehicleClass).map((vehicleClass) => ({
      label: vehicleClass.replace("_", " "),
      value: vehicleClass,
    }));
  }, []);

  const filterFields: DataTableFilterField<VehicleReservationTableData>[] =
    React.useMemo(
      () => [
        {
          id: "customer",
          label: "Customer",
          placeholder: "Filter by customer...",
        },
        {
          id: "status",
          label: "Status",
          placeholder: "Select status...",
          options: statusOptions,
        },
        {
          id: "vehicleClass",
          label: "Vehicle Class",
          placeholder: "Select vehicle class...",
          options: vehicleClassOptions,
        },
      ],
      [statusOptions, vehicleClassOptions]
    );

  const advancedFilterFields: DataTableAdvancedFilterField<VehicleReservationTableData>[] =
    React.useMemo(
      () => [
        {
          id: "customer",
          label: "Customer",
          type: "text",
        },
        {
          id: "pickupLocation",
          label: "Pickup Location",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "select",
          options: statusOptions,
        },
        {
          id: "vehicleClass",
          label: "Vehicle Class",
          type: "select",
          options: vehicleClassOptions,
        },
        {
          id: "pickupDate",
          label: "Pickup Date",
          type: "date",
        },
        {
          id: "returnDate",
          label: "Return Date",
          type: "date",
        },
      ],
      [statusOptions, vehicleClassOptions]
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<VehicleReservationTableData>) => {
      setRowAction(action);

      if (action.type === "view") {
        router.push(`/vehicle-reservations/${action.row.original.id}`);
      } else if (action.type === "edit") {
        setSelectedReservationId(action.row.original.id);
        setIsReservationSheetOpen(true);
      } else if (action.type === "delete") {
        setSelectedReservationId(action.row.original.id);
        setIsDeleteDialogOpen(true);
      }
    },
    [router]
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      // For card view, page is always 1 and we use perPage for pagination
      page: viewMode === ViewMode.Card ? 1 : tablePage,
      perPage: perPage,
      customer: searchParams.get("customer") || "",
      pickupLocation: searchParams.get("pickupLocation") || "",
      status:
        (searchParams.get("status") as ReservationStatus | undefined) ||
        undefined,
      vehicleClass:
        (searchParams.get("vehicleClass") as VehicleClass | undefined) ||
        undefined,
      pickupDateFrom: searchParams.get("pickupDateFrom") || "",
      pickupDateTo: searchParams.get("pickupDateTo") || "",
      returnDateFrom: searchParams.get("returnDateFrom") || "",
      returnDateTo: searchParams.get("returnDateTo") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam
          ? JSON.parse(sortParam)
          : [{ id: "createdAt", desc: true }];
      })(),
    };
  }, [searchParams, tablePage, perPage, viewMode]);

  // Use TanStack Query hook
  const {
    data: reservationsData,
    isLoading,
    refetch,
  } = useReservationsData(searchParamsValues, isDemo);

  // Watch for URL search params changes to handle perPage changes
  useEffect(() => {
    // Check if perPage has increased from previous value
    const currentPerPage = perPage;
    const prevPerPage = allReservations.length > 0 ? allReservations.length : 0;

    if (currentPerPage > prevPerPage && allReservations.length > 0) {
      setIsLoadingMore(true);
    }
  }, [perPage, allReservations.length]);

  // Update all reservations when new data is loaded
  useEffect(() => {
    if (reservationsData?.data?.data) {
      if (viewMode === ViewMode.Card) {
        // Always replace data for the card view when using the perPage approach
        setAllReservations(reservationsData.data.data);
      } else {
        // For table view, always replace data
        setAllReservations(reservationsData.data.data);
      }

      // Turn off loading more indicator if it's on
      setIsLoadingMore(false);
    }
  }, [reservationsData, viewMode]);

  // Force a refetch on component mount to ensure data is loaded on first render
  useEffect(() => {
    // Clear any cached data for this query to ensure a fresh fetch
    queryClient.removeQueries({
      queryKey: reservationKeys.filtered(searchParamsValues),
    });
    // Trigger a refetch
    refetch();
  }, [refetch, queryClient, searchParamsValues]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    setIsLoadingMore(true);
    // The actual perPage update happens in the VehicleReservationsCardView component
  }, []);

  // Handle refresh
  const handleRefresh = React.useCallback(async () => {
    // Reset pagination and data when refreshing
    if (viewMode === ViewMode.Card) {
      // Update URL to reset perPage to default
      const params = new URLSearchParams(searchParams.toString());
      params.set("perPage", "10");
      if (params.has("page")) {
        params.delete("page");
      }
      router.push(`${pathname}?${params.toString()}`);

      // Clear the accumulated reservation data
      setAllReservations([]);
    }
    await refetch();
  }, [refetch, viewMode, router, pathname, searchParams]);

  // Handle success for reservation operations
  const handleSuccess = React.useCallback(() => {
    // Close dialogs
    setIsReservationSheetOpen(false);
    setIsDeleteDialogOpen(false);

    // Reset selection
    setSelectedReservationId(null);

    // Invalidate reservations queries to refetch data
    queryClient.invalidateQueries({ queryKey: reservationKeys.list() });
    router.refresh();
  }, [queryClient, router]);

  // Columns
  const columns = React.useMemo(
    () => getColumns(handleRowAction),
    [handleRowAction]
  );

  return (
    <>
      {viewMode === ViewMode.Table ? (
        <BaseTable<
          VehicleReservationTableData,
          Awaited<ReturnType<typeof getReservationsTableData>>
        >
          data={reservationsData || undefined}
          isLoading={isLoading}
          isDemo={isDemo}
          columns={columns}
          filterFields={filterFields}
          advancedFilterFields={advancedFilterFields}
          getInitialData={(response) => {
            return response?.data?.data ?? [];
          }}
          getPageCount={(response) => {
            return response?.data?.pageCount ?? 1;
          }}
          getRowId={(row) => row.id}
          enableAdvancedTable={false}
          enableFloatingBar={true}
          ToolbarActions={VehicleReservationsTableToolbarActions}
          onRowAction={handleRowAction}
          defaultSortingId="createdAt"
          defaultSortingDesc={true}
          onRefresh={handleRefresh}
        />
      ) : (
        <VehicleReservationsCardView
          reservations={allReservations}
          isLoading={isLoading || isLoadingMore}
          hasMore={
            !!reservationsData?.data?.pageCount &&
            reservationsData.data.pageCount > 1 &&
            allReservations.length < (reservationsData.data.totalItems || 0)
          }
          onLoadMore={handleLoadMore}
        />
      )}

      {/* Reservation Sheet for Add/Edit */}
      <VehicleReservationSheet
        isDemo={isDemo}
        reservationId={selectedReservationId || undefined}
        open={isReservationSheetOpen}
        onOpenChange={setIsReservationSheetOpen}
        onSuccess={handleSuccess}
        isUpdate={!!selectedReservationId}
      />

      {/* Delete Reservation Dialog */}
      {selectedReservationId && (
        <DeleteVehicleReservationDialog
          isDemo={isDemo}
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          reservationId={selectedReservationId}
          onSuccess={handleSuccess}
        />
      )}
    </>
  );
}
