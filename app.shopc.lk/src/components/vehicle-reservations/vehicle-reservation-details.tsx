"use client";

import * as React from "react";
import {
  Info,
  Car,
  Calendar,
  User,
  MapPin,
  CreditCard,
  Phone,
  Mail,
  Clock,
  DollarSign,
  FileText,
  Plane,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useVehicleReservationData } from "@/lib/vehicle-reservations/hooks";
import {
  VehicleReservationTableData,
  VehicleReservationStatus,
  PaymentStatus,
  VehiclePickupLocationType,
  ReservationSource,
} from "@/types/vehicle-reservation";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface VehicleReservationDetailsProps {
  vehicleReservation: VehicleReservationTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isDemo?: boolean;
}

export function VehicleReservationDetails({
  vehicleReservation,
  open,
  onOpenChange,
  isDemo = false,
}: VehicleReservationDetailsProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Fetch complete vehicle reservation data
  const {
    data: fullVehicleReservationResponse,
    isLoading: isLoadingVehicleReservation,
  } = useVehicleReservationData(vehicleReservation?.id || "", isDemo);
  const fullVehicleReservation = fullVehicleReservationResponse?.data;

  // Helper function to get status color
  const getStatusColor = (status: VehicleReservationStatus) => {
    switch (status) {
      case VehicleReservationStatus.INQUIRY:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case VehicleReservationStatus.PENDING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case VehicleReservationStatus.CONFIRMED:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case VehicleReservationStatus.PICKED_UP:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      case VehicleReservationStatus.RETURNED:
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200";
      case VehicleReservationStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case VehicleReservationStatus.NO_SHOW:
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case VehicleReservationStatus.BLOCKED:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Helper function to get payment status color
  const getPaymentStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.PAID:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case PaymentStatus.PARTIAL:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case PaymentStatus.PENDING:
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case PaymentStatus.REFUNDED:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case PaymentStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Helper function to format location type
  const formatLocationType = (type: VehiclePickupLocationType) => {
    switch (type) {
      case VehiclePickupLocationType.OFFICE:
        return "Office";
      case VehiclePickupLocationType.AIRPORT:
        return "Airport";
      case VehiclePickupLocationType.ADDRESS:
        return "Custom Address";
      default:
        return type;
    }
  };

  // Helper function to format reservation source
  const formatReservationSource = (source?: ReservationSource) => {
    if (!source) return "Not specified";
    switch (source) {
      case ReservationSource.ONLINE:
        return "Online";
      case ReservationSource.PHONE:
        return "Phone";
      case ReservationSource.WALK_IN:
        return "Walk-in";
      case ReservationSource.EMAIL:
        return "Email";
      case ReservationSource.AGENT:
        return "Agent";
      case ReservationSource.CORPORATE:
        return "Corporate";
      case ReservationSource.REPEAT_GUEST:
        return "Repeat Guest";
      case ReservationSource.REFERRAL:
        return "Referral";
      case ReservationSource.OTHER:
        return "Other";
      default:
        return source;
    }
  };

  // Content component
  const Content = () => {
    if (isLoadingVehicleReservation) {
      return (
        <div className="space-y-6 p-6">
          <div className="space-y-3">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-1/3" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      );
    }

    if (!fullVehicleReservation) {
      return (
        <div className="p-6 text-center">
          <p className="text-muted-foreground">Vehicle reservation not found</p>
        </div>
      );
    }

    return (
      <ScrollArea className="h-full max-h-[80vh] overflow-y-auto">
        <div className="space-y-6 p-6">
          {/* Header */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">
                {fullVehicleReservation.reservationNumber}
              </h2>
              <div className="flex gap-2">
                <Badge
                  className={cn(
                    "font-medium",
                    getStatusColor(fullVehicleReservation.status)
                  )}
                >
                  {fullVehicleReservation.status}
                </Badge>
                <Badge
                  className={cn(
                    "font-medium",
                    getPaymentStatusColor(fullVehicleReservation.paymentStatus)
                  )}
                >
                  {fullVehicleReservation.paymentStatus}
                </Badge>
              </div>
            </div>
            {fullVehicleReservation.referenceNumber && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <FileText className="h-4 w-4" />
                <span>Reference: {fullVehicleReservation.referenceNumber}</span>
              </div>
            )}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>
                {format(new Date(fullVehicleReservation.pickUpDate), "PPP")} -{" "}
                {format(new Date(fullVehicleReservation.returnDate), "PPP")}
              </span>
            </div>
          </div>

          <Separator />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Information */}
            {fullVehicleReservation.customer && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-muted-foreground" />
                    Customer Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Name
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.customer.firstName}{" "}
                        {fullVehicleReservation.customer.lastName}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Email
                      </label>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm">
                          {fullVehicleReservation.customer.email}
                        </p>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Phone
                      </label>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm">
                          {fullVehicleReservation.customer.phone}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Reservation Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-muted-foreground" />
                  Reservation Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Source
                    </label>
                    <p className="text-sm">
                      {formatReservationSource(
                        fullVehicleReservation.reservationSource
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Pick-up Date
                    </label>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm">
                        {format(
                          new Date(fullVehicleReservation.pickUpDate),
                          "PPP"
                        )}
                      </p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Return Date
                    </label>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm">
                        {format(
                          new Date(fullVehicleReservation.returnDate),
                          "PPP"
                        )}
                      </p>
                    </div>
                  </div>
                  {fullVehicleReservation.actualPickUpTime && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Actual Pick-up Time
                      </label>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm">
                          {format(
                            new Date(fullVehicleReservation.actualPickUpTime),
                            "PPp"
                          )}
                        </p>
                      </div>
                    </div>
                  )}
                  {fullVehicleReservation.actualReturnTime && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Actual Return Time
                      </label>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm">
                          {format(
                            new Date(fullVehicleReservation.actualReturnTime),
                            "PPp"
                          )}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Pick-up Location */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  Pick-up Location
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Location Type
                    </label>
                    <p className="text-sm">
                      {formatLocationType(
                        fullVehicleReservation.pickUpLocationType
                      )}
                    </p>
                  </div>
                  {fullVehicleReservation.pickUpAddress && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Address
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.pickUpAddress}
                      </p>
                    </div>
                  )}
                  {fullVehicleReservation.pickUpInstructions && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Instructions
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.pickUpInstructions}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Return Location */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  Return Location
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Location Type
                    </label>
                    <p className="text-sm">
                      {formatLocationType(
                        fullVehicleReservation.returnLocationType
                      )}
                    </p>
                  </div>
                  {fullVehicleReservation.returnAddress && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Address
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.returnAddress}
                      </p>
                    </div>
                  )}
                  {fullVehicleReservation.returnInstructions && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Instructions
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.returnInstructions}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Flight Information */}
            {(fullVehicleReservation.arrivalFlightNumber ||
              fullVehicleReservation.departureFlightNumber) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Plane className="h-5 w-5 text-muted-foreground" />
                    Flight Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    {fullVehicleReservation.arrivalFlightNumber && (
                      <>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">
                            Arrival Flight
                          </label>
                          <p className="text-sm">
                            {fullVehicleReservation.arrivalFlightNumber}
                          </p>
                        </div>
                        {fullVehicleReservation.arrivalAirline && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              Arrival Airline
                            </label>
                            <p className="text-sm">
                              {fullVehicleReservation.arrivalAirline}
                            </p>
                          </div>
                        )}
                        {fullVehicleReservation.flightArrivalDate && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              Arrival Date & Time
                            </label>
                            <p className="text-sm">
                              {format(
                                new Date(
                                  fullVehicleReservation.flightArrivalDate
                                ),
                                "PPP"
                              )}
                              {fullVehicleReservation.flightArrivalTime &&
                                ` at ${fullVehicleReservation.flightArrivalTime}`}
                            </p>
                          </div>
                        )}
                      </>
                    )}
                    {fullVehicleReservation.departureFlightNumber && (
                      <>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">
                            Departure Flight
                          </label>
                          <p className="text-sm">
                            {fullVehicleReservation.departureFlightNumber}
                          </p>
                        </div>
                        {fullVehicleReservation.departureAirline && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              Departure Airline
                            </label>
                            <p className="text-sm">
                              {fullVehicleReservation.departureAirline}
                            </p>
                          </div>
                        )}
                        {fullVehicleReservation.flightDepartureDate && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              Departure Date & Time
                            </label>
                            <p className="text-sm">
                              {format(
                                new Date(
                                  fullVehicleReservation.flightDepartureDate
                                ),
                                "PPP"
                              )}
                              {fullVehicleReservation.flightDepartureTime &&
                                ` at ${fullVehicleReservation.flightDepartureTime}`}
                            </p>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-muted-foreground" />
                  Financial Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Daily Rate
                    </label>
                    <p className="text-sm font-mono">
                      ${fullVehicleReservation.dailyRate}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Subtotal
                    </label>
                    <p className="text-sm font-mono">
                      ${fullVehicleReservation.subtotal}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Total Amount
                    </label>
                    <p className="text-sm font-mono font-bold">
                      ${fullVehicleReservation.total}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Total Paid
                    </label>
                    <p className="text-sm font-mono text-green-600">
                      ${fullVehicleReservation.totalPaid}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Outstanding Balance
                    </label>
                    <p className="text-sm font-mono text-red-600">
                      ${fullVehicleReservation.outstandingBalance}
                    </p>
                  </div>
                  {fullVehicleReservation.totalRefunded !== "0.00" && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Total Refunded
                      </label>
                      <p className="text-sm font-mono text-blue-600">
                        ${fullVehicleReservation.totalRefunded}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Vehicles Information */}
            {fullVehicleReservation.vehicles &&
              fullVehicleReservation.vehicles.length > 0 && (
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Car className="h-5 w-5 text-muted-foreground" />
                      Vehicles ({fullVehicleReservation.vehicles.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {fullVehicleReservation.vehicles.map((vehicle, index) => (
                        <div key={vehicle.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium">
                              {vehicle.vehicle.make} {vehicle.vehicle.model} (
                              {vehicle.vehicle.year})
                            </h4>
                            <Badge variant="outline">
                              {vehicle.vehicle.vehicleNumber}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <label className="font-medium text-muted-foreground">
                                Color
                              </label>
                              <p>{vehicle.vehicle.color}</p>
                            </div>
                            <div>
                              <label className="font-medium text-muted-foreground">
                                Daily Rate
                              </label>
                              <p className="font-mono">
                                ${vehicle.vehicleDailyRate}
                              </p>
                            </div>
                            <div>
                              <label className="font-medium text-muted-foreground">
                                Total
                              </label>
                              <p className="font-mono font-bold">
                                ${vehicle.total}
                              </p>
                            </div>
                            {vehicle.vehicleWithDriver && (
                              <div>
                                <label className="font-medium text-muted-foreground">
                                  With Driver
                                </label>
                                <p className="text-green-600">Yes</p>
                              </div>
                            )}
                            {vehicle.notes && (
                              <div className="md:col-span-3">
                                <label className="font-medium text-muted-foreground">
                                  Notes
                                </label>
                                <p>{vehicle.notes}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

            {/* Notes and Additional Information */}
            {(fullVehicleReservation.notes ||
              fullVehicleReservation.cancellationReason) && (
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                    Notes & Additional Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {fullVehicleReservation.notes && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Notes
                      </label>
                      <p className="text-sm mt-1">
                        {fullVehicleReservation.notes}
                      </p>
                    </div>
                  )}
                  {fullVehicleReservation.cancellationReason && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Cancellation Reason
                      </label>
                      <p className="text-sm mt-1 text-red-600">
                        {fullVehicleReservation.cancellationReason}
                      </p>
                      {fullVehicleReservation.cancellationDate && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Cancelled on{" "}
                          {format(
                            new Date(fullVehicleReservation.cancellationDate),
                            "PPP"
                          )}
                        </p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Metadata */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  Metadata
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Created By
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <User className="h-4 w-4 text-muted-foreground" />
                      {fullVehicleReservation.createdBy?.name || "System"}
                    </p>
                  </div>
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Created At
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {format(
                        new Date(fullVehicleReservation.createdAt),
                        "PPP"
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Last Updated
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {format(
                        new Date(fullVehicleReservation.updatedAt),
                        "PPP"
                      )}
                    </p>
                  </div>
                  {fullVehicleReservation.confirmationSent && (
                    <div>
                      <label className="font-medium text-muted-foreground">
                        Confirmation Sent
                      </label>
                      <p className="flex items-center gap-2 mt-1 text-green-600">
                        <Mail className="h-4 w-4" />
                        {fullVehicleReservation.confirmationSentAt &&
                          format(
                            new Date(fullVehicleReservation.confirmationSentAt),
                            "PPP"
                          )}
                      </p>
                    </div>
                  )}
                  {fullVehicleReservation.reminderSent && (
                    <div>
                      <label className="font-medium text-muted-foreground">
                        Reminder Sent
                      </label>
                      <p className="flex items-center gap-2 mt-1 text-blue-600">
                        <Mail className="h-4 w-4" />
                        {fullVehicleReservation.reminderSentAt &&
                          format(
                            new Date(fullVehicleReservation.reminderSentAt),
                            "PPP"
                          )}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </ScrollArea>
    );
  };

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle className="flex items-center justify-between">
              <span>Vehicle Reservation Details</span>
              {isDemo && (
                <Badge variant="outline" className="ml-2">
                  Demo Mode
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>
          <Content />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="max-h-[90vh]">
        <DrawerHeader className="border-b">
          <DrawerTitle className="flex items-center justify-between">
            <span>Vehicle Reservation Details</span>
            {isDemo && (
              <Badge variant="outline" className="ml-2">
                Demo Mode
              </Badge>
            )}
          </DrawerTitle>
        </DrawerHeader>
        <Content />
      </DrawerContent>
    </Drawer>
  );
}
