"use client";

import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { VehicleReservationStatus } from "@/types/vehicle-reservation";
import * as demoApi from "@/lib/vehicle-reservations/demo";
import * as api from "@/lib/vehicle-reservations/api";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import { Loader2, Check } from "lucide-react";
import { useState } from "react";

interface VehicleReservationStatusBadgeProps {
  vehicleReservationId: string;
  status: VehicleReservationStatus;
  isDemo?: boolean;
  disabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (vehicleReservationId: string, newStatus: VehicleReservationStatus) => void;
}

export function VehicleReservationStatusBadge({
  vehicleReservationId,
  status,
  isDemo = false,
  disabled = false,
  onRefresh,
  onStatusUpdate,
}: VehicleReservationStatusBadgeProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async (newStatus: VehicleReservationStatus) => {
    // Don't make API call if status is already the same
    if (newStatus === status) {
      setIsOpen(false);
      return;
    }

    setIsOpen(false);
    setIsUpdating(true);

    try {
      const result = isDemo
        ? await demoApi.updateDemoVehicleReservationApi(vehicleReservationId, {
            status: newStatus,
          })
        : await api.updateVehicleReservationApi(vehicleReservationId, {
            status: newStatus,
          });

      if (result.status === ApiStatus.SUCCESS) {
        toast.success(
          `Vehicle reservation status updated to ${getStatusLabel(newStatus)}`
        );
        // Use the new status update handler if available
        if (onStatusUpdate) {
          onStatusUpdate(vehicleReservationId, newStatus);
        } else {
          // Fallback to onRefresh for backward compatibility
          onRefresh?.();
        }
      } else {
        toast.error(result.message || "Failed to update status");
      }
    } catch (error) {
      toast.error("Failed to update vehicle reservation status");
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusLabel = (status: VehicleReservationStatus): string => {
    switch (status) {
      case VehicleReservationStatus.INQUIRY:
        return "Inquiry";
      case VehicleReservationStatus.PENDING:
        return "Pending";
      case VehicleReservationStatus.CONFIRMED:
        return "Confirmed";
      case VehicleReservationStatus.PICKED_UP:
        return "Picked Up";
      case VehicleReservationStatus.RETURNED:
        return "Returned";
      case VehicleReservationStatus.CANCELLED:
        return "Cancelled";
      case VehicleReservationStatus.NO_SHOW:
        return "No Show";
      case VehicleReservationStatus.BLOCKED:
        return "Blocked";
      default:
        return status;
    }
  };

  const getStatusVariant = (status: VehicleReservationStatus) => {
    switch (status) {
      case VehicleReservationStatus.INQUIRY:
        return "secondary";
      case VehicleReservationStatus.PENDING:
        return "outline";
      case VehicleReservationStatus.CONFIRMED:
        return "default";
      case VehicleReservationStatus.PICKED_UP:
        return "default";
      case VehicleReservationStatus.RETURNED:
        return "default";
      case VehicleReservationStatus.CANCELLED:
        return "destructive";
      case VehicleReservationStatus.NO_SHOW:
        return "destructive";
      case VehicleReservationStatus.BLOCKED:
        return "destructive";
      default:
        return "outline";
    }
  };

  const statusOptions = [
    {
      value: VehicleReservationStatus.INQUIRY,
      label: "Inquiry",
      variant: "secondary" as const,
    },
    {
      value: VehicleReservationStatus.PENDING,
      label: "Pending",
      variant: "outline" as const,
    },
    {
      value: VehicleReservationStatus.CONFIRMED,
      label: "Confirmed",
      variant: "default" as const,
    },
    {
      value: VehicleReservationStatus.PICKED_UP,
      label: "Picked Up",
      variant: "default" as const,
    },
    {
      value: VehicleReservationStatus.RETURNED,
      label: "Returned",
      variant: "default" as const,
    },
    {
      value: VehicleReservationStatus.CANCELLED,
      label: "Cancelled",
      variant: "destructive" as const,
    },
    {
      value: VehicleReservationStatus.NO_SHOW,
      label: "No Show",
      variant: "destructive" as const,
    },
    {
      value: VehicleReservationStatus.BLOCKED,
      label: "Blocked",
      variant: "destructive" as const,
    },
  ];

  return (
    <DropdownMenu open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Badge
          variant={getStatusVariant(status)}
          className={`${
            disabled
              ? "cursor-not-allowed opacity-50"
              : "cursor-pointer hover:opacity-80"
          } transition-opacity`}
        >
          {isUpdating ? (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Updating...</span>
            </div>
          ) : (
            <span>{getStatusLabel(status)}</span>
          )}
        </Badge>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[120px]">
        {statusOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleStatusUpdate(option.value)}
            disabled={isUpdating || disabled}
            className="flex items-center justify-between"
          >
            <span>{option.label}</span>
            {status === option.value && <Check className="h-3 w-3" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
