"use client";

import * as React from "react";
import {
  Info,
  Car,
  Calendar,
  User,
  MapPin,
  CreditCard,
  Phone,
  Mail,
  Clock,
  DollarSign,
  FileText,
  Plane,
  Printer,
  Download,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useVehicleReservationData } from "@/lib/vehicle-reservations/hooks";
import {
  VehicleReservationTableData,
  VehicleReservationStatus,
  PaymentStatus,
  VehiclePickupLocationType,
  ReservationSource,
} from "@/types/vehicle-reservation";
import { LoadingIndicator } from "@/components/shared/loading-indicator";
import { toast } from "sonner";

interface VehicleReservationDetailsContentProps {
  vehicleReservation: VehicleReservationTableData;
  isDemo?: boolean;
}

export function VehicleReservationDetailsContent({
  vehicleReservation,
  isDemo = false,
}: VehicleReservationDetailsContentProps) {
  const printRef = React.useRef<HTMLDivElement>(null);

  // Fetch complete vehicle reservation data
  const {
    data: fullVehicleReservationResponse,
    isLoading: isLoadingVehicleReservation,
  } = useVehicleReservationData(vehicleReservation.id, isDemo);
  const fullVehicleReservation = fullVehicleReservationResponse?.data;

  // Print functionality
  const handlePrint = React.useCallback(() => {
    const printWindow = window.open("", "_blank");
    if (!printWindow || !printRef.current) return;

    const printContent = printRef.current.cloneNode(true) as HTMLElement;

    // Create a complete HTML document for printing
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Vehicle Reservation Details - ${
            fullVehicleReservation?.reservationNumber || "Reservation"
          }</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .print-header h1 { margin: 0; font-size: 24px; color: #333; }
            .print-header p { margin: 5px 0 0 0; color: #666; }
            .section { margin-bottom: 25px; page-break-inside: avoid; }
            .section h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
            .field { margin-bottom: 10px; }
            .field label { font-weight: bold; color: #555; display: block; margin-bottom: 3px; font-size: 12px; }
            .field div { color: #333; }
            .badge { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
            @media print { 
              body { margin: 0; }
              .no-print { display: none !important; }
              .page-break { page-break-before: always; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Vehicle Reservation Details</h1>
            <p>Generated on ${format(new Date(), "PPP 'at' p")}</p>
          </div>
          ${printContent.innerHTML}
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }, [fullVehicleReservation]);

  // Download PDF functionality
  const handleDownloadPDF = React.useCallback(async () => {
    if (!printRef.current || !fullVehicleReservation) return;

    try {
      const { default: jsPDF } = await import("jspdf");
      const { default: html2canvas } = await import("html2canvas");

      const element = printRef.current;
      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(
        `vehicle-reservation-${fullVehicleReservation.reservationNumber}.pdf`
      );
      toast.success("PDF downloaded successfully");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF");
    }
  }, [fullVehicleReservation]);

  // Helper functions
  const getStatusColor = (status: VehicleReservationStatus) => {
    switch (status) {
      case VehicleReservationStatus.INQUIRY:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case VehicleReservationStatus.PENDING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case VehicleReservationStatus.CONFIRMED:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case VehicleReservationStatus.PICKED_UP:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      case VehicleReservationStatus.RETURNED:
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200";
      case VehicleReservationStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case VehicleReservationStatus.NO_SHOW:
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case VehicleReservationStatus.BLOCKED:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getPaymentStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.PAID:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case PaymentStatus.PARTIAL:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case PaymentStatus.PENDING:
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case PaymentStatus.REFUNDED:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case PaymentStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const formatLocationType = (type: VehiclePickupLocationType) => {
    switch (type) {
      case VehiclePickupLocationType.OFFICE:
        return "Office";
      case VehiclePickupLocationType.AIRPORT:
        return "Airport";
      case VehiclePickupLocationType.ADDRESS:
        return "Custom Address";
      default:
        return type;
    }
  };

  const formatReservationSource = (source?: ReservationSource) => {
    if (!source) return "Not specified";
    switch (source) {
      case ReservationSource.ONLINE:
        return "Online";
      case ReservationSource.PHONE:
        return "Phone";
      case ReservationSource.WALK_IN:
        return "Walk-in";
      case ReservationSource.EMAIL:
        return "Email";
      case ReservationSource.AGENT:
        return "Agent";
      case ReservationSource.CORPORATE:
        return "Corporate";
      case ReservationSource.REPEAT_GUEST:
        return "Repeat Guest";
      case ReservationSource.REFERRAL:
        return "Referral";
      case ReservationSource.OTHER:
        return "Other";
      default:
        return source;
    }
  };

  if (isLoadingVehicleReservation) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingIndicator />
      </div>
    );
  }

  if (!fullVehicleReservation) {
    return (
      <div className="p-6 text-center">
        <p className="text-muted-foreground">Vehicle reservation not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex items-center justify-between no-print">
        <div className="flex items-center gap-2">
          <Badge
            className={cn(
              "font-medium",
              getStatusColor(fullVehicleReservation.status)
            )}
          >
            {fullVehicleReservation.status}
          </Badge>
          <Badge
            className={cn(
              "font-medium",
              getPaymentStatusColor(fullVehicleReservation.paymentStatus)
            )}
          >
            {fullVehicleReservation.paymentStatus}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadPDF}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download PDF
          </Button>
        </div>
      </div>

      {/* Printable Content */}
      <div ref={printRef} className="space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h2 className="text-2xl font-bold">
            {fullVehicleReservation.reservationNumber}
          </h2>
          {fullVehicleReservation.referenceNumber && (
            <p className="text-muted-foreground">
              Reference: {fullVehicleReservation.referenceNumber}
            </p>
          )}
          <p className="text-sm text-muted-foreground">
            {format(new Date(fullVehicleReservation.pickUpDate), "PPP")} -{" "}
            {format(new Date(fullVehicleReservation.returnDate), "PPP")}
          </p>
        </div>

        {/* Accordion Sections */}
        <Accordion
          type="multiple"
          defaultValue={["basic", "customer", "financial"]}
          className="w-full"
        >
          {/* Basic Information */}
          <AccordionItem value="basic">
            <AccordionTrigger className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              Basic Information
            </AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Reservation Number
                    </label>
                    <p className="text-sm">
                      {fullVehicleReservation.reservationNumber}
                    </p>
                  </div>
                  {fullVehicleReservation.referenceNumber && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Reference Number
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.referenceNumber}
                      </p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Status
                    </label>
                    <p className="text-sm">{fullVehicleReservation.status}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Payment Status
                    </label>
                    <p className="text-sm">
                      {fullVehicleReservation.paymentStatus}
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Reservation Source
                    </label>
                    <p className="text-sm">
                      {formatReservationSource(
                        fullVehicleReservation.reservationSource
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Pick-up Date
                    </label>
                    <p className="text-sm">
                      {format(
                        new Date(fullVehicleReservation.pickUpDate),
                        "PPP"
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Return Date
                    </label>
                    <p className="text-sm">
                      {format(
                        new Date(fullVehicleReservation.returnDate),
                        "PPP"
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Vehicle Count
                    </label>
                    <p className="text-sm">
                      {fullVehicleReservation.vehicles?.length || 0}
                    </p>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Customer Information */}
          {fullVehicleReservation.customer && (
            <AccordionItem value="customer">
              <AccordionTrigger className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Customer Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Name
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.customer.firstName}{" "}
                        {fullVehicleReservation.customer.lastName}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Email
                      </label>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm">
                          {fullVehicleReservation.customer.email}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Phone
                      </label>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm">
                          {fullVehicleReservation.customer.phone}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          )}

          {/* Location Information */}
          <AccordionItem value="location">
            <AccordionTrigger className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Location Information
            </AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Pick-up Location */}
                <div className="space-y-3">
                  <h4 className="font-medium">Pick-up Location</h4>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Location Type
                    </label>
                    <p className="text-sm">
                      {formatLocationType(
                        fullVehicleReservation.pickUpLocationType
                      )}
                    </p>
                  </div>
                  {fullVehicleReservation.pickUpAddress && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Address
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.pickUpAddress}
                      </p>
                    </div>
                  )}
                  {fullVehicleReservation.pickUpInstructions && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Instructions
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.pickUpInstructions}
                      </p>
                    </div>
                  )}
                </div>
                {/* Return Location */}
                <div className="space-y-3">
                  <h4 className="font-medium">Return Location</h4>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Location Type
                    </label>
                    <p className="text-sm">
                      {formatLocationType(
                        fullVehicleReservation.returnLocationType
                      )}
                    </p>
                  </div>
                  {fullVehicleReservation.returnAddress && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Address
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.returnAddress}
                      </p>
                    </div>
                  )}
                  {fullVehicleReservation.returnInstructions && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Instructions
                      </label>
                      <p className="text-sm">
                        {fullVehicleReservation.returnInstructions}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Financial Information */}
          <AccordionItem value="financial">
            <AccordionTrigger className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Financial Information
            </AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Daily Rate
                    </label>
                    <p className="text-sm font-mono">
                      ${fullVehicleReservation.dailyRate}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Subtotal
                    </label>
                    <p className="text-sm font-mono">
                      ${fullVehicleReservation.subtotal}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Total Amount
                    </label>
                    <p className="text-sm font-mono font-bold">
                      ${fullVehicleReservation.total}
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Total Paid
                    </label>
                    <p className="text-sm font-mono text-green-600">
                      ${fullVehicleReservation.totalPaid}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Outstanding Balance
                    </label>
                    <p className="text-sm font-mono text-red-600">
                      ${fullVehicleReservation.outstandingBalance}
                    </p>
                  </div>
                  {fullVehicleReservation.totalRefunded !== "0.00" && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Total Refunded
                      </label>
                      <p className="text-sm font-mono text-blue-600">
                        ${fullVehicleReservation.totalRefunded}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Vehicles Information */}
          {fullVehicleReservation.vehicles &&
            fullVehicleReservation.vehicles.length > 0 && (
              <AccordionItem value="vehicles">
                <AccordionTrigger className="flex items-center gap-2">
                  <Car className="h-4 w-4" />
                  Vehicles ({fullVehicleReservation.vehicles.length})
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4">
                    {fullVehicleReservation.vehicles.map((vehicle, index) => (
                      <div key={vehicle.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium">
                            {vehicle.vehicle.make} {vehicle.vehicle.model} (
                            {vehicle.vehicle.year})
                          </h4>
                          <Badge variant="outline">
                            {vehicle.vehicle.vehicleNumber}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <label className="font-medium text-muted-foreground">
                              Color
                            </label>
                            <p>{vehicle.vehicle.color}</p>
                          </div>
                          <div>
                            <label className="font-medium text-muted-foreground">
                              Daily Rate
                            </label>
                            <p className="font-mono">
                              ${vehicle.vehicleDailyRate}
                            </p>
                          </div>
                          <div>
                            <label className="font-medium text-muted-foreground">
                              Total
                            </label>
                            <p className="font-mono font-bold">
                              ${vehicle.total}
                            </p>
                          </div>
                          {vehicle.vehicleWithDriver && (
                            <div>
                              <label className="font-medium text-muted-foreground">
                                With Driver
                              </label>
                              <p className="text-green-600">Yes</p>
                            </div>
                          )}
                          {vehicle.notes && (
                            <div className="md:col-span-3">
                              <label className="font-medium text-muted-foreground">
                                Notes
                              </label>
                              <p>{vehicle.notes}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

          {/* Notes */}
          {(fullVehicleReservation.notes ||
            fullVehicleReservation.cancellationReason) && (
            <AccordionItem value="notes">
              <AccordionTrigger className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Notes & Additional Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  {fullVehicleReservation.notes && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Notes
                      </label>
                      <p className="text-sm mt-1">
                        {fullVehicleReservation.notes}
                      </p>
                    </div>
                  )}
                  {fullVehicleReservation.cancellationReason && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Cancellation Reason
                      </label>
                      <p className="text-sm mt-1 text-red-600">
                        {fullVehicleReservation.cancellationReason}
                      </p>
                      {fullVehicleReservation.cancellationDate && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Cancelled on{" "}
                          {format(
                            new Date(fullVehicleReservation.cancellationDate),
                            "PPP"
                          )}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          )}

          {/* Metadata */}
          <AccordionItem value="metadata">
            <AccordionTrigger className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Metadata
            </AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <label className="font-medium text-muted-foreground">
                    Created By
                  </label>
                  <p className="flex items-center gap-2 mt-1">
                    <User className="h-4 w-4 text-muted-foreground" />
                    {fullVehicleReservation.createdBy?.name || "System"}
                  </p>
                </div>
                <div>
                  <label className="font-medium text-muted-foreground">
                    Created At
                  </label>
                  <p className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    {format(new Date(fullVehicleReservation.createdAt), "PPP")}
                  </p>
                </div>
                <div>
                  <label className="font-medium text-muted-foreground">
                    Last Updated
                  </label>
                  <p className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    {format(new Date(fullVehicleReservation.updatedAt), "PPP")}
                  </p>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
