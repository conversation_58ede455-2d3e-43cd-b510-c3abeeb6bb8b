import { ApiResponse } from "./common";

// Backend enums - must match exactly with backend
export enum VehicleReservationStatus {
  INQUIRY = "INQUIRY",
  PENDING = "PENDING",
  CONFIRMED = "CONFIRMED",
  PICKED_UP = "PICKED_UP",
  RETURNED = "RETURNED",
  CANCELLED = "CANCELLED",
  NO_SHOW = "NO_SHOW",
  BLOCKED = "BLOCKED",
}

export enum VehiclePickupLocationType {
  OFFICE = "OFFICE",
  AIRPORT = "AIRPORT",
  ADDRESS = "ADDRESS",
}

export enum ReservationSource {
  ONLINE = "ONLINE",
  PHONE = "PHONE",
  WALK_IN = "WALK_IN",
  EMAIL = "EMAIL",
  AGENT = "AGENT",
  CORPORATE = "CORPORATE",
  REPEAT_GUEST = "REPEAT_GUEST",
  REFERRAL = "REFERRAL",
  OTHER = "OTHER",
}

export enum PaymentStatus {
  PENDING = "PENDING",
  PARTIAL = "PARTIAL",
  PAID = "PAID",
  REFUNDED = "REFUNDED",
  CANCELLED = "CANCELLED",
}

export enum TaxType {
  PERCENTAGE = "PERCENTAGE",
  FIXED = "FIXED",
}

export enum DiscountType {
  PERCENTAGE = "PERCENTAGE",
  FIXED = "FIXED",
}

// Vehicle DTO for reservation vehicles
export interface VehicleReservationVehicleDto {
  id: string;
  vehicleId: string;
  vehicle: {
    id: string;
    vehicleNumber: string;
    make: string;
    model: string;
    year: number;
    color: string;
  };
  vehicleDailyRate: string;
  subtotal: string;
  total: string;
  vehicleDiscountType?: DiscountType;
  vehicleDiscountAmount?: string;
  vehicleDiscountReason?: string;
  vehicleWithDriver?: boolean;
  vehicleDriverId?: string;
  vehicleDriverNotes?: string;
  vehicleExternalDriverName?: string;
  vehicleExternalDriverPhone?: string;
  vehicleExternalDriverLicense?: string;
  vehicleOrder: number;
  vehicleTaxRateId?: string;
  vehicleTaxRate?: {
    id: string;
    name: string;
    rate: string;
    type: TaxType;
  };
  pickUpOdometer?: number;
  returnOdometer?: number;
  pickUpFuelLevel?: number;
  returnFuelLevel?: number;
  pickUpConditionNotes?: string;
  returnConditionNotes?: string;
  notes?: string;
}

// Backend DTO: VehicleReservationDto
export interface VehicleReservationDto {
  id: string;
  businessId: string;
  reservationNumber: string;
  referenceNumber?: string;
  customerId?: string;
  customer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  pickUpDate: string;
  returnDate: string;
  actualPickUpTime?: string;
  actualReturnTime?: string;
  status: VehicleReservationStatus;
  reservationSource?: ReservationSource;
  dailyRate: string;
  discountType?: DiscountType;
  discountAmount?: string;
  discountReason?: string;
  subtotal: string;
  total: string;
  paymentStatus: PaymentStatus;
  totalPaid: string;
  totalRefunded: string;
  outstandingBalance: string;
  taxRateId?: string;
  taxRate?: {
    id: string;
    name: string;
    rate: string;
    type: TaxType;
  };
  pickUpLocationType: VehiclePickupLocationType;
  pickUpLocationId?: string;
  pickUpAddress?: string;
  pickUpInstructions?: string;
  returnLocationType: VehiclePickupLocationType;
  returnLocationId?: string;
  returnAddress?: string;
  returnInstructions?: string;
  flightArrivalDate?: string;
  flightArrivalTime?: string;
  flightDepartureDate?: string;
  flightDepartureTime?: string;
  arrivalFlightNumber?: string;
  departureFlightNumber?: string;
  arrivalAirline?: string;
  departureAirline?: string;
  cancellationReason?: string;
  cancellationDate?: string;
  notes?: string;
  confirmationSent: boolean;
  confirmationSentAt?: string;
  reminderSent: boolean;
  reminderSentAt?: string;
  vehicles: VehicleReservationVehicleDto[];
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    id: string;
    name: string;
    avatar?: string;
  };
  updatedBy?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

// Backend DTO: VehicleReservationListDto
export interface VehicleReservationListDto {
  id: string;
  reservationNumber: string;
  referenceNumber?: string;
  customerId?: string;
  customer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  pickUpDate: string;
  returnDate: string;
  status: VehicleReservationStatus;
  reservationSource?: ReservationSource;
  paymentStatus: PaymentStatus;
  total: string;
  totalPaid: string;
  outstandingBalance: string;
  vehicleCount: number;
  createdAt: string;
  updatedAt: string;
}

// Backend DTO: VehicleReservationSlimDto
export interface VehicleReservationSlimDto {
  id: string;
  reservationNumber: string;
  referenceNumber?: string;
  customerName?: string;
  pickUpDate: string;
  returnDate: string;
  status: VehicleReservationStatus;
  paymentStatus: PaymentStatus;
  total: string;
  vehicleCount: number;
  createdAt: string;
}

// Backend DTO: CreateVehicleReservationVehicleDto
export interface CreateVehicleReservationVehicleDto {
  vehicleId: string;
  vehicleDailyRate: string;
  vehicleDiscountType?: DiscountType;
  vehicleDiscountAmount?: string;
  vehicleDiscountReason?: string;
  vehicleWithDriver?: boolean;
  vehicleDriverId?: string;
  vehicleDriverNotes?: string;
  vehicleExternalDriverName?: string;
  vehicleExternalDriverPhone?: string;
  vehicleExternalDriverLicense?: string;
  vehicleOrder?: number;
  vehicleTaxRateId?: string;
  notes?: string;
}

// Backend DTO: CreateVehicleReservationDto
export interface CreateVehicleReservationDto {
  reservationNumber: string;
  referenceNumber?: string;
  customerId?: string;
  pickUpDate: string;
  returnDate: string;
  status?: VehicleReservationStatus;
  reservationSource?: ReservationSource;
  dailyRate: string;
  discountType?: DiscountType;
  discountAmount?: string;
  discountReason?: string;
  taxRateId?: string;
  pickUpLocationType: VehiclePickupLocationType;
  pickUpLocationId?: string;
  pickUpAddress?: string;
  pickUpInstructions?: string;
  returnLocationType: VehiclePickupLocationType;
  returnLocationId?: string;
  returnAddress?: string;
  returnInstructions?: string;
  flightArrivalDate?: string;
  flightArrivalTime?: string;
  flightDepartureDate?: string;
  flightDepartureTime?: string;
  arrivalFlightNumber?: string;
  departureFlightNumber?: string;
  arrivalAirline?: string;
  departureAirline?: string;
  notes?: string;
  vehicles: CreateVehicleReservationVehicleDto[];
}

// Backend DTO: UpdateVehicleReservationVehicleDto
export interface UpdateVehicleReservationVehicleDto
  extends Partial<CreateVehicleReservationVehicleDto> {
  id?: string;
  pickUpOdometer?: number;
  returnOdometer?: number;
  pickUpFuelLevel?: number;
  returnFuelLevel?: number;
  pickUpConditionNotes?: string;
  returnConditionNotes?: string;
}

// Backend DTO: UpdateVehicleReservationDto
export interface UpdateVehicleReservationDto
  extends Partial<CreateVehicleReservationDto> {
  actualPickUpTime?: string;
  actualReturnTime?: string;
  cancellationReason?: string;
  cancellationDate?: string;
  vehicles?: UpdateVehicleReservationVehicleDto[];
}

// Backend DTO: BulkCreateVehicleReservationDto
export interface BulkCreateVehicleReservationDto {
  reservations: CreateVehicleReservationDto[];
}

// Response DTOs
export interface VehicleReservationResponse {
  status: string;
  message: string;
  data: VehicleReservationDto | null;
}

export interface VehicleReservationPaginatedResponse {
  status: string;
  message: string;
  data: PaginatedVehicleReservationsResponseDto | null;
}

export interface VehicleReservationIdResponse {
  status: string;
  message: string;
  data: VehicleReservationIdResponseDto | null;
}

export interface BulkVehicleReservationIdsResponse {
  status: string;
  message: string;
  data: BulkVehicleReservationIdsResponseDto | null;
}

export interface BulkDeleteVehicleReservationResponse {
  status: string;
  message: string;
  data: BulkDeleteVehicleReservationResponseDto | null;
}

export interface VehicleReservationNumberAvailabilityResponse {
  status: string;
  message: string;
  data: VehicleReservationNumberAvailabilityResponseDto | null;
}

export interface SimpleVehicleReservationResponse {
  status: string;
  message: string;
  data: VehicleReservationSlimDto[] | null;
}

export interface BulkUpdateVehicleReservationStatusResponse {
  status: string;
  message: string;
  data: BulkUpdateVehicleReservationStatusResponseDto | null;
}

export interface DeleteVehicleReservationResponse {
  status: string;
  message: string;
  data: DeleteVehicleReservationResponseDto | null;
}

// Backend response DTOs
export interface VehicleReservationIdResponseDto {
  id: string;
}

export interface BulkVehicleReservationIdsResponseDto {
  ids: string[];
  created: number;
  message: string;
  failed?: Array<{
    reservation: CreateVehicleReservationDto;
    error: string;
  }>;
}

export interface BulkDeleteVehicleReservationResponseDto {
  deleted: number;
  message: string;
  deletedIds: string[];
  failed?: Array<{
    id: string;
    error: string;
  }>;
}

export interface DeleteVehicleReservationResponseDto {
  success: boolean;
  message: string;
}

export interface VehicleReservationNumberAvailabilityResponseDto {
  available: boolean;
  reservationNumber: string;
  message: string;
}

export interface BulkUpdateVehicleReservationStatusResponseDto {
  updated: number;
  message: string;
  updatedIds: string[];
  failed?: Array<{
    id: string;
    error: string;
  }>;
}

export interface PaginatedVehicleReservationsResponseDto {
  data: VehicleReservationListDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

// Table data interface - optimized for table display
export interface VehicleReservationTableData {
  id: string;
  reservationNumber: string;
  referenceNumber?: string;
  customerName?: string;
  customerId?: string;
  pickUpDate: string;
  returnDate: string;
  status: VehicleReservationStatus;
  paymentStatus: PaymentStatus;
  total: string;
  totalPaid: string;
  outstandingBalance: string;
  vehicleCount: number;
  reservationSource?: ReservationSource;
  createdAt: string;
  updatedAt: string;
}

// Form values interface for UI components
export interface VehicleReservationFormValues {
  reservationNumber: string;
  referenceNumber?: string;
  customerId?: string;
  pickUpDate: string;
  returnDate: string;
  status?: VehicleReservationStatus;
  reservationSource?: ReservationSource;
  dailyRate: string;
  discountType?: DiscountType;
  discountAmount?: string;
  discountReason?: string;
  taxRateId?: string;
  pickUpLocationType: VehiclePickupLocationType;
  pickUpLocationId?: string;
  pickUpAddress?: string;
  pickUpInstructions?: string;
  returnLocationType: VehiclePickupLocationType;
  returnLocationId?: string;
  returnAddress?: string;
  returnInstructions?: string;
  flightArrivalDate?: string;
  flightArrivalTime?: string;
  flightDepartureDate?: string;
  flightDepartureTime?: string;
  arrivalFlightNumber?: string;
  departureFlightNumber?: string;
  arrivalAirline?: string;
  departureAirline?: string;
  notes?: string;
  vehicles: CreateVehicleReservationVehicleDto[];
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type VehicleReservation = VehicleReservationDto;
export type SimpleVehicleReservationData = VehicleReservationSlimDto;
export type VehicleReservationPaginatedData =
  PaginatedVehicleReservationsResponseDto;
