// Re-export all types from the main types file for convenience
export * from "@/types/package-reservation";

// Additional utility types specific to the package-reservations library
export interface PackageReservationFilters {
  reservationNumber?: string;
  referenceNumber?: string;
  packageId?: string;
  status?: string;
  paymentStatus?: string;
  from?: string;
  to?: string;
}

export interface PackageReservationSortConfig {
  field: string;
  direction: "asc" | "desc";
}

export interface PackageReservationTableConfig {
  page: number;
  perPage: number;
  filters?: PackageReservationFilters;
  sort?: PackageReservationSortConfig[];
}

// Guest management types
export interface GuestManagementData {
  guestId?: string;
  isPrimaryGuest?: boolean;
  primaryGuestId?: string;
  relationshipToPrimary?: string;
  participationStatus?: string;
  notes?: string;
  // Guest creation fields if guestId is not provided
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  dateOfBirth?: string;
  nationality?: string;
  identificationType?: string;
  identificationNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

// Communication types
export interface CommunicationOptions {
  sendEmail?: boolean;
  sendSms?: boolean;
  sendWhatsApp?: boolean;
  customMessage?: string;
}

// Availability check types
export interface AvailabilityCheckParams {
  startDate: string;
  endDate: string;
  adults: number;
  children?: number;
  packageIds?: string[];
}

export interface AvailablePackage {
  id: string;
  name: string;
  packageCode?: string;
  packageType?: string;
  description?: string;
  basePrice: string;
  maxGuests: number;
  available: boolean;
  availableSlots?: number;
}

// Pricing calculation types
export interface PricingCalculationParams {
  packageId: string;
  startDate: string;
  endDate: string;
  adults: number;
  children?: number;
  discountType?: string;
  discountValue?: string;
  taxType?: string;
}

export interface PricingBreakdown {
  basePrice: string;
  adultPrice: string;
  childPrice: string;
  subtotal: string;
  discountAmount?: string;
  taxAmount?: string;
  total: string;
  pricePerNight?: string;
  numberOfNights?: number;
}

// Form state types
export interface PackageReservationFormState {
  step: number;
  isLoading: boolean;
  errors: Record<string, string>;
  data: Partial<any>;
}

// Action types for form management
export type PackageReservationFormAction =
  | { type: "SET_STEP"; payload: number }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERRORS"; payload: Record<string, string> }
  | { type: "SET_DATA"; payload: Partial<any> }
  | { type: "RESET_FORM" };

// Status badge types
export interface StatusBadgeConfig {
  label: string;
  variant: "default" | "secondary" | "destructive" | "outline" | "success" | "warning";
  color?: string;
}

// Export utility type for status configurations
export type PackageReservationStatusConfig = Record<string, StatusBadgeConfig>;
export type PaymentStatusConfig = Record<string, StatusBadgeConfig>;
