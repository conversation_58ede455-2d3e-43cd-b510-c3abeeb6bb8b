import { z } from "zod";
import {
  PackageReservationStatus,
  PaymentStatus,
  ReservationSource,
  TaxType,
  DiscountType,
} from "@/types/package-reservation";

// Backend DTO: CreatePackageReservationDto
export const createPackageReservationSchema = z.object({
  reservationNumber: z.string().min(1, "Reservation number is required"),
  referenceNumber: z.string().optional(),
  packageId: z.string().uuid("Invalid package ID"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  totalNumberOfGuests: z
    .number()
    .min(1, "Total guests must be at least 1")
    .optional(),
  numberOfAdults: z.number().min(1, "Number of adults must be at least 1"),
  numberOfChildren: z
    .number()
    .min(0, "Number of children cannot be negative")
    .optional(),
  status: z
    .enum([
      PackageReservationStatus.INQUIRY,
      PackageReservationStatus.PENDING,
      PackageReservationStatus.CONFIRMED,
      PackageReservationStatus.PARTIALLY_PAID,
      PackageReservationStatus.PAID,
      PackageReservationStatus.IN_PROGRESS,
      PackageReservationStatus.COMPLETED,
      PackageReservationStatus.CANCELLED,
      PackageReservationStatus.NO_SHOW,
      PackageReservationStatus.REFUNDED,
    ])
    .optional()
    .default(PackageReservationStatus.PENDING),
  reservationSource: z
    .enum([
      ReservationSource.ONLINE,
      ReservationSource.PHONE,
      ReservationSource.WALK_IN,
      ReservationSource.EMAIL,
      ReservationSource.AGENT,
      ReservationSource.CORPORATE,
      ReservationSource.REPEAT_GUEST,
      ReservationSource.REFERRAL,
      ReservationSource.OTHER,
    ])
    .optional(),
  paymentStatus: z
    .enum([
      PaymentStatus.PENDING,
      PaymentStatus.PARTIAL,
      PaymentStatus.PAID,
      PaymentStatus.REFUNDED,
      PaymentStatus.CANCELLED,
    ])
    .optional()
    .default(PaymentStatus.PENDING),
  subtotal: z.string().optional(),
  total: z.string().optional(),
  depositPaid: z.string().optional(),
  balanceDue: z.string().optional(),
  discountType: z
    .enum([DiscountType.PERCENTAGE, DiscountType.FIXED_AMOUNT])
    .optional(),
  discountValue: z.string().optional(),
  discountAmount: z.string().optional(),
  taxType: z
    .enum([TaxType.INCLUSIVE, TaxType.EXCLUSIVE, TaxType.OUT_OF_SCOPE])
    .optional()
    .default(TaxType.INCLUSIVE),
  taxAmount: z.string().optional(),
  notes: z.string().optional(),
  cancellationReason: z.string().optional(),
  cancellationDate: z.string().optional(),
  confirmationSent: z.boolean().optional(),
  confirmationSentAt: z.string().optional(),
  reminderSent: z.boolean().optional(),
  reminderSentAt: z.string().optional(),
});

// Backend DTO: UpdatePackageReservationDto
export const updatePackageReservationSchema = z.object({
  reservationNumber: z
    .string()
    .min(1, "Reservation number is required")
    .optional(),
  referenceNumber: z.string().optional(),
  packageId: z.string().uuid("Invalid package ID").optional(),
  startDate: z.string().min(1, "Start date is required").optional(),
  endDate: z.string().min(1, "End date is required").optional(),
  totalNumberOfGuests: z
    .number()
    .min(1, "Total guests must be at least 1")
    .optional(),
  numberOfAdults: z
    .number()
    .min(1, "Number of adults must be at least 1")
    .optional(),
  numberOfChildren: z
    .number()
    .min(0, "Number of children cannot be negative")
    .optional(),
  status: z
    .enum([
      PackageReservationStatus.INQUIRY,
      PackageReservationStatus.PENDING,
      PackageReservationStatus.CONFIRMED,
      PackageReservationStatus.PARTIALLY_PAID,
      PackageReservationStatus.PAID,
      PackageReservationStatus.IN_PROGRESS,
      PackageReservationStatus.COMPLETED,
      PackageReservationStatus.CANCELLED,
      PackageReservationStatus.NO_SHOW,
      PackageReservationStatus.REFUNDED,
    ])
    .optional(),
  reservationSource: z
    .enum([
      ReservationSource.ONLINE,
      ReservationSource.PHONE,
      ReservationSource.WALK_IN,
      ReservationSource.EMAIL,
      ReservationSource.AGENT,
      ReservationSource.CORPORATE,
      ReservationSource.REPEAT_GUEST,
      ReservationSource.REFERRAL,
      ReservationSource.OTHER,
    ])
    .optional(),
  paymentStatus: z
    .enum([
      PaymentStatus.PENDING,
      PaymentStatus.PARTIAL,
      PaymentStatus.PAID,
      PaymentStatus.REFUNDED,
      PaymentStatus.CANCELLED,
    ])
    .optional(),
  subtotal: z.string().optional(),
  total: z.string().optional(),
  depositPaid: z.string().optional(),
  balanceDue: z.string().optional(),
  discountType: z
    .enum([DiscountType.PERCENTAGE, DiscountType.FIXED_AMOUNT])
    .optional(),
  discountValue: z.string().optional(),
  discountAmount: z.string().optional(),
  taxType: z
    .enum([TaxType.INCLUSIVE, TaxType.EXCLUSIVE, TaxType.OUT_OF_SCOPE])
    .optional(),
  taxAmount: z.string().optional(),
  notes: z.string().optional(),
  cancellationReason: z.string().optional(),
  cancellationDate: z.string().optional(),
  confirmationSent: z.boolean().optional(),
  confirmationSentAt: z.string().optional(),
  reminderSent: z.boolean().optional(),
  reminderSentAt: z.string().optional(),
});

// Get package reservations schema
export const getPackageReservationsSchema = z.object({
  page: z.number().min(1).optional(),
  perPage: z.number().min(1).max(100).optional(),
  from: z.string().optional(),
  to: z.string().optional(),
  reservationNumber: z.string().optional(),
  referenceNumber: z.string().optional(),
  packageId: z.string().uuid().optional(),
  status: z.string().optional(),
  paymentStatus: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.string(),
        operator: z.enum([
          "iLike",
          "notILike",
          "eq",
          "ne",
          "isEmpty",
          "isNotEmpty",
        ]),
        type: z.enum(["text", "select", "date", "number"]),
        rowId: z.string(),
      })
    )
    .optional(),
  joinOperator: z.enum(["and", "or"]).optional(),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional(),
});

// Bulk create package reservations schema
export const bulkCreatePackageReservationsSchema = z.object({
  packageReservations: z.array(createPackageReservationSchema),
});

// Bulk delete package reservations schema
export const bulkDeletePackageReservationsSchema = z.object({
  packageReservationIds: z.array(z.string().uuid()),
});

// Bulk update package reservation status schema
export const bulkUpdatePackageReservationStatusSchema = z.object({
  packageReservationIds: z.array(z.string().uuid()),
  status: z.enum([
    PackageReservationStatus.INQUIRY,
    PackageReservationStatus.PENDING,
    PackageReservationStatus.CONFIRMED,
    PackageReservationStatus.PARTIALLY_PAID,
    PackageReservationStatus.PAID,
    PackageReservationStatus.IN_PROGRESS,
    PackageReservationStatus.COMPLETED,
    PackageReservationStatus.CANCELLED,
    PackageReservationStatus.NO_SHOW,
    PackageReservationStatus.REFUNDED,
  ]),
});

// Check package availability schema
export const checkPackageAvailabilitySchema = z.object({
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  adults: z.number().min(1, "Number of adults must be at least 1"),
  children: z
    .number()
    .min(0, "Number of children cannot be negative")
    .optional(),
  packageIds: z.array(z.string().uuid()).optional(),
});

// Calculate package pricing schema
export const calculatePackagePricingSchema = z.object({
  packageId: z.string().uuid("Invalid package ID"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  adults: z.number().min(1, "Number of adults must be at least 1"),
  children: z
    .number()
    .min(0, "Number of children cannot be negative")
    .optional(),
  discountType: z
    .enum([DiscountType.PERCENTAGE, DiscountType.FIXED_AMOUNT])
    .optional(),
  discountValue: z.string().optional(),
  taxType: z
    .enum([TaxType.INCLUSIVE, TaxType.EXCLUSIVE, TaxType.OUT_OF_SCOPE])
    .optional(),
});

// Form schema for UI components (includes additional fields for form handling)
export const packageReservationFormSchema = z.object({
  reservationNumber: z.string().min(1, "Reservation number is required"),
  referenceNumber: z.string().optional(),
  packageId: z.string().uuid("Invalid package ID"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  totalNumberOfGuests: z
    .number()
    .min(1, "Total guests must be at least 1")
    .optional(),
  numberOfAdults: z.number().min(1, "Number of adults must be at least 1"),
  numberOfChildren: z
    .number()
    .min(0, "Number of children cannot be negative")
    .optional(),
  status: z
    .enum([
      PackageReservationStatus.INQUIRY,
      PackageReservationStatus.PENDING,
      PackageReservationStatus.CONFIRMED,
      PackageReservationStatus.PARTIALLY_PAID,
      PackageReservationStatus.PAID,
      PackageReservationStatus.IN_PROGRESS,
      PackageReservationStatus.COMPLETED,
      PackageReservationStatus.CANCELLED,
      PackageReservationStatus.NO_SHOW,
      PackageReservationStatus.REFUNDED,
    ])
    .optional(),
  reservationSource: z
    .enum([
      ReservationSource.ONLINE,
      ReservationSource.PHONE,
      ReservationSource.WALK_IN,
      ReservationSource.EMAIL,
      ReservationSource.AGENT,
      ReservationSource.CORPORATE,
      ReservationSource.REPEAT_GUEST,
      ReservationSource.REFERRAL,
      ReservationSource.OTHER,
    ])
    .optional(),
  paymentStatus: z
    .enum([
      PaymentStatus.PENDING,
      PaymentStatus.PARTIAL,
      PaymentStatus.PAID,
      PaymentStatus.REFUNDED,
      PaymentStatus.CANCELLED,
    ])
    .optional(),
  subtotal: z.string().optional(),
  total: z.string().optional(),
  depositPaid: z.string().optional(),
  balanceDue: z.string().optional(),
  discountType: z
    .enum([DiscountType.PERCENTAGE, DiscountType.FIXED_AMOUNT])
    .optional(),
  discountValue: z.string().optional(),
  discountAmount: z.string().optional(),
  taxType: z
    .enum([TaxType.INCLUSIVE, TaxType.EXCLUSIVE, TaxType.OUT_OF_SCOPE])
    .optional(),
  taxAmount: z.string().optional(),
  notes: z.string().optional(),
  cancellationReason: z.string().optional(),
  cancellationDate: z.string().optional(),
  confirmationSent: z.boolean().optional(),
  confirmationSentAt: z.string().optional(),
  reminderSent: z.boolean().optional(),
  reminderSentAt: z.string().optional(),
  guests: z.array(z.any()).optional(),
});

// Export types
export type CreatePackageReservationSchema = z.infer<
  typeof createPackageReservationSchema
>;
export type UpdatePackageReservationSchema = z.infer<
  typeof updatePackageReservationSchema
>;
export type GetPackageReservationsSchema = z.infer<
  typeof getPackageReservationsSchema
>;
export type BulkCreatePackageReservationsSchema = z.infer<
  typeof bulkCreatePackageReservationsSchema
>;
export type BulkDeletePackageReservationsSchema = z.infer<
  typeof bulkDeletePackageReservationsSchema
>;
export type BulkUpdatePackageReservationStatusSchema = z.infer<
  typeof bulkUpdatePackageReservationStatusSchema
>;
export type CheckPackageAvailabilitySchema = z.infer<
  typeof checkPackageAvailabilitySchema
>;
export type CalculatePackagePricingSchema = z.infer<
  typeof calculatePackagePricingSchema
>;
export type PackageReservationFormSchema = z.infer<
  typeof packageReservationFormSchema
>;

// Guest management schemas
export const addGuestToPackageReservationSchema = z.object({
  guestId: z.string().uuid().optional(),
  isPrimaryGuest: z.boolean().optional(),
  primaryGuestId: z.string().uuid().optional(),
  relationshipToPrimary: z.string().optional(),
  participationStatus: z.string().optional(),
  notes: z.string().optional(),
  // Guest creation fields if guestId is not provided
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  email: z.string().email("Invalid email format").optional(),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  nationality: z.string().optional(),
  identificationType: z.string().optional(),
  identificationNumber: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
});

export const removeGuestFromPackageReservationSchema = z.object({
  reason: z.string().optional(),
  notes: z.string().optional(),
});

// Communication schemas
export const sendPackageConfirmationSchema = z.object({
  sendEmail: z.boolean().optional(),
  sendSms: z.boolean().optional(),
  sendWhatsApp: z.boolean().optional(),
  customMessage: z.string().optional(),
});

export const sendPackageReminderSchema = z.object({
  sendEmail: z.boolean().optional(),
  sendSms: z.boolean().optional(),
  sendWhatsApp: z.boolean().optional(),
  customMessage: z.string().optional(),
  reminderType: z.string().optional(),
});

// Confirmation and cancellation schemas
export const confirmPackageReservationSchema = z.object({
  confirmationNotes: z.string().optional(),
  sendConfirmation: z.boolean().optional(),
  paymentStatus: z
    .enum([
      PaymentStatus.PENDING,
      PaymentStatus.PARTIAL,
      PaymentStatus.PAID,
      PaymentStatus.REFUNDED,
      PaymentStatus.CANCELLED,
    ])
    .optional(),
  depositPaid: z.string().optional(),
});

export const cancelPackageReservationSchema = z.object({
  cancellationReason: z.string().min(1, "Cancellation reason is required"),
  cancellationNotes: z.string().optional(),
  refundAmount: z.string().optional(),
  sendCancellationNotice: z.boolean().optional(),
});

// Export additional schema types
export type AddGuestToPackageReservationSchema = z.infer<
  typeof addGuestToPackageReservationSchema
>;
export type RemoveGuestFromPackageReservationSchema = z.infer<
  typeof removeGuestFromPackageReservationSchema
>;
export type SendPackageConfirmationSchema = z.infer<
  typeof sendPackageConfirmationSchema
>;
export type SendPackageReminderSchema = z.infer<
  typeof sendPackageReminderSchema
>;
export type ConfirmPackageReservationSchema = z.infer<
  typeof confirmPackageReservationSchema
>;
export type CancelPackageReservationSchema = z.infer<
  typeof cancelPackageReservationSchema
>;
