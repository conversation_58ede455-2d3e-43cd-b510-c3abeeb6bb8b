import {
  PackageReservationDto,
  PackageReservationTableData,
  PackageReservationStatus,
  PaymentStatus,
  ReservationSource,
  TaxType,
  DiscountType,
} from "@/types/package-reservation";

// Format package reservation status for display
export function formatPackageReservationStatus(status: PackageReservationStatus): string {
  switch (status) {
    case PackageReservationStatus.INQUIRY:
      return "Inquiry";
    case PackageReservationStatus.PENDING:
      return "Pending";
    case PackageReservationStatus.CONFIRMED:
      return "Confirmed";
    case PackageReservationStatus.PARTIALLY_PAID:
      return "Partially Paid";
    case PackageReservationStatus.PAID:
      return "Paid";
    case PackageReservationStatus.IN_PROGRESS:
      return "In Progress";
    case PackageReservationStatus.COMPLETED:
      return "Completed";
    case PackageReservationStatus.CANCELLED:
      return "Cancelled";
    case PackageReservationStatus.NO_SHOW:
      return "No Show";
    case PackageReservationStatus.REFUNDED:
      return "Refunded";
    default:
      return "Unknown";
  }
}

// Get status color for badges/indicators
export function getPackageReservationStatusColor(status: PackageReservationStatus): string {
  switch (status) {
    case PackageReservationStatus.INQUIRY:
      return "bg-blue-100 text-blue-800";
    case PackageReservationStatus.PENDING:
      return "bg-yellow-100 text-yellow-800";
    case PackageReservationStatus.CONFIRMED:
      return "bg-green-100 text-green-800";
    case PackageReservationStatus.PARTIALLY_PAID:
      return "bg-orange-100 text-orange-800";
    case PackageReservationStatus.PAID:
      return "bg-emerald-100 text-emerald-800";
    case PackageReservationStatus.IN_PROGRESS:
      return "bg-indigo-100 text-indigo-800";
    case PackageReservationStatus.COMPLETED:
      return "bg-green-100 text-green-800";
    case PackageReservationStatus.CANCELLED:
      return "bg-red-100 text-red-800";
    case PackageReservationStatus.NO_SHOW:
      return "bg-gray-100 text-gray-800";
    case PackageReservationStatus.REFUNDED:
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Format payment status for display
export function formatPaymentStatus(status: PaymentStatus): string {
  switch (status) {
    case PaymentStatus.PENDING:
      return "Pending";
    case PaymentStatus.PARTIAL:
      return "Partial";
    case PaymentStatus.PAID:
      return "Paid";
    case PaymentStatus.REFUNDED:
      return "Refunded";
    case PaymentStatus.CANCELLED:
      return "Cancelled";
    default:
      return "Unknown";
  }
}

// Get payment status color for badges/indicators
export function getPaymentStatusColor(status: PaymentStatus): string {
  switch (status) {
    case PaymentStatus.PENDING:
      return "bg-yellow-100 text-yellow-800";
    case PaymentStatus.PARTIAL:
      return "bg-orange-100 text-orange-800";
    case PaymentStatus.PAID:
      return "bg-green-100 text-green-800";
    case PaymentStatus.REFUNDED:
      return "bg-purple-100 text-purple-800";
    case PaymentStatus.CANCELLED:
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Format reservation source for display
export function formatReservationSource(source: ReservationSource): string {
  switch (source) {
    case ReservationSource.ONLINE:
      return "Online";
    case ReservationSource.PHONE:
      return "Phone";
    case ReservationSource.WALK_IN:
      return "Walk-in";
    case ReservationSource.EMAIL:
      return "Email";
    case ReservationSource.AGENT:
      return "Agent";
    case ReservationSource.CORPORATE:
      return "Corporate";
    case ReservationSource.REPEAT_GUEST:
      return "Repeat Guest";
    case ReservationSource.REFERRAL:
      return "Referral";
    case ReservationSource.OTHER:
      return "Other";
    default:
      return "Unknown";
  }
}

// Format currency amount
export function formatCurrency(amount: string | number, currency = "USD"): string {
  const num = typeof amount === "string" ? parseFloat(amount) : amount;
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
  }).format(num);
}

// Format date range
export function formatDateRange(startDate: Date | string, endDate: Date | string): string {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const startFormatted = start.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
  
  const endFormatted = end.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
  
  return `${startFormatted} - ${endFormatted}`;
}

// Calculate duration in days
export function calculateDuration(startDate: Date | string, endDate: Date | string): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

// Calculate duration in nights
export function calculateNights(startDate: Date | string, endDate: Date | string): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = end.getTime() - start.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
}

// Format guest count
export function formatGuestCount(adults: number, children?: number): string {
  if (!children || children === 0) {
    return `${adults} Adult${adults !== 1 ? "s" : ""}`;
  }
  return `${adults} Adult${adults !== 1 ? "s" : ""}, ${children} Child${children !== 1 ? "ren" : ""}`;
}

// Generate reservation number
export function generateReservationNumber(prefix = "PKG", year?: number): string {
  const currentYear = year || new Date().getFullYear();
  const timestamp = Date.now().toString().slice(-6);
  return `${prefix}-${currentYear}-${timestamp}`;
}

// Validate reservation dates
export function validateReservationDates(startDate: string, endDate: string) {
  const errors: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (start < today) {
    errors.push("Start date cannot be in the past");
  }

  if (end <= start) {
    errors.push("End date must be after start date");
  }

  const maxAdvanceBooking = new Date();
  maxAdvanceBooking.setFullYear(maxAdvanceBooking.getFullYear() + 2);
  
  if (start > maxAdvanceBooking) {
    errors.push("Start date cannot be more than 2 years in advance");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Calculate total amount with discount and tax
export function calculateTotalAmount(
  subtotal: number,
  discountType?: DiscountType,
  discountValue?: number,
  taxType?: TaxType,
  taxRate?: number
): {
  subtotal: number;
  discountAmount: number;
  taxAmount: number;
  total: number;
} {
  let discountAmount = 0;
  let taxAmount = 0;

  // Calculate discount
  if (discountType && discountValue) {
    if (discountType === DiscountType.PERCENTAGE) {
      discountAmount = (subtotal * discountValue) / 100;
    } else if (discountType === DiscountType.FIXED_AMOUNT) {
      discountAmount = discountValue;
    }
  }

  const afterDiscount = subtotal - discountAmount;

  // Calculate tax
  if (taxType && taxRate) {
    if (taxType === TaxType.EXCLUSIVE) {
      taxAmount = (afterDiscount * taxRate) / 100;
    } else if (taxType === TaxType.INCLUSIVE) {
      // Tax is already included in the price
      taxAmount = (afterDiscount * taxRate) / (100 + taxRate);
    }
    // OUT_OF_SCOPE means no tax
  }

  const total = taxType === TaxType.INCLUSIVE 
    ? afterDiscount 
    : afterDiscount + taxAmount;

  return {
    subtotal,
    discountAmount,
    taxAmount,
    total,
  };
}

// Search package reservations
export function searchPackageReservations(
  reservations: PackageReservationTableData[],
  searchTerm: string
): PackageReservationTableData[] {
  if (!searchTerm.trim()) return reservations;

  const term = searchTerm.toLowerCase();
  return reservations.filter(
    (reservation) =>
      reservation.reservationNumber.toLowerCase().includes(term) ||
      reservation.referenceNumber?.toLowerCase().includes(term) ||
      reservation.packageName.toLowerCase().includes(term) ||
      reservation.primaryGuestName?.toLowerCase().includes(term)
  );
}

// Get reservation statistics
export function getReservationStats(reservations: PackageReservationDto[]) {
  const stats = {
    total: reservations.length,
    byStatus: {} as Record<PackageReservationStatus, number>,
    byPaymentStatus: {} as Record<PaymentStatus, number>,
    totalRevenue: 0,
    averageValue: 0,
  };

  // Initialize status counters
  Object.values(PackageReservationStatus).forEach(status => {
    stats.byStatus[status] = 0;
  });
  
  Object.values(PaymentStatus).forEach(status => {
    stats.byPaymentStatus[status] = 0;
  });

  reservations.forEach((reservation) => {
    stats.byStatus[reservation.status]++;
    stats.byPaymentStatus[reservation.paymentStatus]++;
    stats.totalRevenue += parseFloat(reservation.total);
  });

  stats.averageValue = stats.total > 0 ? stats.totalRevenue / stats.total : 0;

  return stats;
}

// Check if reservation can be cancelled
export function canCancelReservation(reservation: PackageReservationDto): boolean {
  const nonCancellableStatuses = [
    PackageReservationStatus.CANCELLED,
    PackageReservationStatus.COMPLETED,
    PackageReservationStatus.REFUNDED,
    PackageReservationStatus.NO_SHOW,
  ];
  
  return !nonCancellableStatuses.includes(reservation.status);
}

// Check if reservation can be confirmed
export function canConfirmReservation(reservation: PackageReservationDto): boolean {
  const confirmableStatuses = [
    PackageReservationStatus.INQUIRY,
    PackageReservationStatus.PENDING,
  ];
  
  return confirmableStatuses.includes(reservation.status);
}

// Legacy utility functions for backward compatibility
export const getReservationStatusBadgeColor = getPackageReservationStatusColor;
export const formatReservationStatus = formatPackageReservationStatus;
