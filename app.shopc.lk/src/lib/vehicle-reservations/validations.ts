import { z } from "zod";
import {
  VehicleReservationStatus,
  VehiclePickupLocationType,
  ReservationSource,
  PaymentStatus,
  TaxType,
  DiscountType,
} from "@/types/vehicle-reservation";

// Backend DTO: CreateVehicleReservationVehicleDto
export const createVehicleReservationVehicleSchema = z.object({
  vehicleId: z.string().uuid("Invalid vehicle ID"),
  vehicleDailyRate: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Daily rate must be a valid decimal"),
  vehicleDiscountType: z
    .enum([DiscountType.PERCENTAGE, DiscountType.FIXED])
    .optional(),
  vehicleDiscountAmount: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Discount amount must be a valid decimal")
    .optional(),
  vehicleDiscountReason: z.string().optional(),
  vehicleWithDriver: z.boolean().optional().default(false),
  vehicleDriverId: z.string().uuid("Invalid driver ID").optional(),
  vehicleDriverNotes: z.string().optional(),
  vehicleExternalDriverName: z.string().optional(),
  vehicleExternalDriverPhone: z.string().optional(),
  vehicleExternalDriverLicense: z.string().optional(),
  vehicleOrder: z
    .number()
    .min(1, "Vehicle order must be at least 1")
    .optional()
    .default(1),
  vehicleTaxRateId: z.string().uuid("Invalid tax rate ID").optional(),
  notes: z.string().optional(),
});

// Backend DTO: CreateVehicleReservationDto
export const createVehicleReservationSchema = z.object({
  reservationNumber: z
    .string()
    .min(1, "Reservation number is required")
    .max(191, "Reservation number too long"),
  referenceNumber: z.string().max(191, "Reference number too long").optional(),
  customerId: z.string().uuid("Invalid customer ID").optional(),
  pickUpDate: z.string().datetime("Invalid pickup date format"),
  returnDate: z.string().datetime("Invalid return date format"),
  status: z
    .enum([
      VehicleReservationStatus.INQUIRY,
      VehicleReservationStatus.PENDING,
      VehicleReservationStatus.CONFIRMED,
      VehicleReservationStatus.PICKED_UP,
      VehicleReservationStatus.RETURNED,
      VehicleReservationStatus.CANCELLED,
      VehicleReservationStatus.NO_SHOW,
      VehicleReservationStatus.BLOCKED,
    ])
    .optional()
    .default(VehicleReservationStatus.PENDING),
  reservationSource: z
    .enum([
      ReservationSource.ONLINE,
      ReservationSource.PHONE,
      ReservationSource.WALK_IN,
      ReservationSource.EMAIL,
      ReservationSource.AGENT,
      ReservationSource.CORPORATE,
      ReservationSource.REPEAT_GUEST,
      ReservationSource.REFERRAL,
      ReservationSource.OTHER,
    ])
    .optional(),
  dailyRate: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Daily rate must be a valid decimal"),
  discountType: z
    .enum([DiscountType.PERCENTAGE, DiscountType.FIXED])
    .optional(),
  discountAmount: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Discount amount must be a valid decimal")
    .optional(),
  discountReason: z.string().optional(),
  taxRateId: z.string().uuid("Invalid tax rate ID").optional(),
  pickUpLocationType: z.enum([
    VehiclePickupLocationType.OFFICE,
    VehiclePickupLocationType.AIRPORT,
    VehiclePickupLocationType.ADDRESS,
  ]),
  pickUpLocationId: z.string().uuid("Invalid pickup location ID").optional(),
  pickUpAddress: z.string().optional(),
  pickUpInstructions: z.string().optional(),
  returnLocationType: z.enum([
    VehiclePickupLocationType.OFFICE,
    VehiclePickupLocationType.AIRPORT,
    VehiclePickupLocationType.ADDRESS,
  ]),
  returnLocationId: z.string().uuid("Invalid return location ID").optional(),
  returnAddress: z.string().optional(),
  returnInstructions: z.string().optional(),
  flightArrivalDate: z.string().optional(),
  flightArrivalTime: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)")
    .optional(),
  flightDepartureDate: z.string().optional(),
  flightDepartureTime: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)")
    .optional(),
  arrivalFlightNumber: z.string().optional(),
  departureFlightNumber: z.string().optional(),
  arrivalAirline: z.string().optional(),
  departureAirline: z.string().optional(),
  notes: z.string().optional(),
  vehicles: z
    .array(createVehicleReservationVehicleSchema)
    .min(1, "At least one vehicle is required"),
});

// Backend DTO: UpdateVehicleReservationVehicleDto
export const updateVehicleReservationVehicleSchema = z.object({
  id: z.string().uuid("Invalid vehicle reservation vehicle ID").optional(),
  vehicleId: z.string().uuid("Invalid vehicle ID").optional(),
  vehicleDailyRate: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Daily rate must be a valid decimal")
    .optional(),
  vehicleDiscountType: z
    .enum([DiscountType.PERCENTAGE, DiscountType.FIXED])
    .optional(),
  vehicleDiscountAmount: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Discount amount must be a valid decimal")
    .optional(),
  vehicleDiscountReason: z.string().optional(),
  vehicleWithDriver: z.boolean().optional(),
  vehicleDriverId: z.string().uuid("Invalid driver ID").optional(),
  vehicleDriverNotes: z.string().optional(),
  vehicleExternalDriverName: z.string().optional(),
  vehicleExternalDriverPhone: z.string().optional(),
  vehicleExternalDriverLicense: z.string().optional(),
  vehicleOrder: z
    .number()
    .min(1, "Vehicle order must be at least 1")
    .optional(),
  vehicleTaxRateId: z.string().uuid("Invalid tax rate ID").optional(),
  pickUpOdometer: z
    .number()
    .min(0, "Odometer reading must be non-negative")
    .optional(),
  returnOdometer: z
    .number()
    .min(0, "Odometer reading must be non-negative")
    .optional(),
  pickUpFuelLevel: z
    .number()
    .min(0, "Fuel level must be between 0-100")
    .max(100, "Fuel level must be between 0-100")
    .optional(),
  returnFuelLevel: z
    .number()
    .min(0, "Fuel level must be between 0-100")
    .max(100, "Fuel level must be between 0-100")
    .optional(),
  pickUpConditionNotes: z.string().optional(),
  returnConditionNotes: z.string().optional(),
  notes: z.string().optional(),
});

// Backend DTO: UpdateVehicleReservationDto
export const updateVehicleReservationSchema = z.object({
  reservationNumber: z
    .string()
    .min(1, "Reservation number is required")
    .max(191, "Reservation number too long")
    .optional(),
  referenceNumber: z.string().max(191, "Reference number too long").optional(),
  customerId: z.string().uuid("Invalid customer ID").optional(),
  pickUpDate: z.string().datetime("Invalid pickup date format").optional(),
  returnDate: z.string().datetime("Invalid return date format").optional(),
  actualPickUpTime: z
    .string()
    .datetime("Invalid actual pickup time format")
    .optional(),
  actualReturnTime: z
    .string()
    .datetime("Invalid actual return time format")
    .optional(),
  status: z
    .enum([
      VehicleReservationStatus.INQUIRY,
      VehicleReservationStatus.PENDING,
      VehicleReservationStatus.CONFIRMED,
      VehicleReservationStatus.PICKED_UP,
      VehicleReservationStatus.RETURNED,
      VehicleReservationStatus.CANCELLED,
      VehicleReservationStatus.NO_SHOW,
      VehicleReservationStatus.BLOCKED,
    ])
    .optional(),
  reservationSource: z
    .enum([
      ReservationSource.ONLINE,
      ReservationSource.PHONE,
      ReservationSource.WALK_IN,
      ReservationSource.EMAIL,
      ReservationSource.AGENT,
      ReservationSource.CORPORATE,
      ReservationSource.REPEAT_GUEST,
      ReservationSource.REFERRAL,
      ReservationSource.OTHER,
    ])
    .optional(),
  dailyRate: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Daily rate must be a valid decimal")
    .optional(),
  discountType: z
    .enum([DiscountType.PERCENTAGE, DiscountType.FIXED])
    .optional(),
  discountAmount: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Discount amount must be a valid decimal")
    .optional(),
  discountReason: z.string().optional(),
  taxRateId: z.string().uuid("Invalid tax rate ID").optional(),
  pickUpLocationType: z
    .enum([
      VehiclePickupLocationType.OFFICE,
      VehiclePickupLocationType.AIRPORT,
      VehiclePickupLocationType.ADDRESS,
    ])
    .optional(),
  pickUpLocationId: z.string().uuid("Invalid pickup location ID").optional(),
  pickUpAddress: z.string().optional(),
  pickUpInstructions: z.string().optional(),
  returnLocationType: z
    .enum([
      VehiclePickupLocationType.OFFICE,
      VehiclePickupLocationType.AIRPORT,
      VehiclePickupLocationType.ADDRESS,
    ])
    .optional(),
  returnLocationId: z.string().uuid("Invalid return location ID").optional(),
  returnAddress: z.string().optional(),
  returnInstructions: z.string().optional(),
  flightArrivalDate: z.string().optional(),
  flightArrivalTime: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)")
    .optional(),
  flightDepartureDate: z.string().optional(),
  flightDepartureTime: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)")
    .optional(),
  arrivalFlightNumber: z.string().optional(),
  departureFlightNumber: z.string().optional(),
  arrivalAirline: z.string().optional(),
  departureAirline: z.string().optional(),
  cancellationReason: z.string().optional(),
  cancellationDate: z
    .string()
    .datetime("Invalid cancellation date format")
    .optional(),
  notes: z.string().optional(),
  vehicles: z.array(updateVehicleReservationVehicleSchema).optional(),
});

// Schema for getting vehicle reservations with filters
export const getVehicleReservationsSchema = z.object({
  page: z.number().min(1, "Page must be at least 1").optional().default(1),
  limit: z
    .number()
    .min(1, "Limit must be at least 1")
    .max(100, "Limit cannot exceed 100")
    .optional()
    .default(10),
  from: z.string().optional(),
  to: z.string().optional(),
  reservationNumber: z.string().optional(),
  referenceNumber: z.string().optional(),
  status: z.string().optional(),
  paymentStatus: z.string().optional(),
  customerId: z.string().uuid("Invalid customer ID").optional(),
  filters: z.string().optional(),
  joinOperator: z.enum(["and", "or"]).optional().default("and"),
  sort: z.string().optional(),
});

// Bulk create schema for vehicle reservations
export const bulkCreateVehicleReservationsSchema = z.object({
  reservations: z
    .array(createVehicleReservationSchema)
    .min(1, "At least one reservation is required"),
});

// Schema for bulk deletion
export const bulkDeleteVehicleReservationsSchema = z.object({
  vehicleReservationIds: z
    .array(z.string().uuid("Invalid vehicle reservation ID"))
    .min(1, "At least one ID is required"),
});

// Schema for bulk status update
export const bulkUpdateVehicleReservationStatusSchema = z.object({
  vehicleReservationIds: z
    .array(z.string().uuid("Invalid vehicle reservation ID"))
    .min(1, "At least one ID is required"),
  status: z.enum([
    VehicleReservationStatus.INQUIRY,
    VehicleReservationStatus.PENDING,
    VehicleReservationStatus.CONFIRMED,
    VehicleReservationStatus.PICKED_UP,
    VehicleReservationStatus.RETURNED,
    VehicleReservationStatus.CANCELLED,
    VehicleReservationStatus.NO_SHOW,
    VehicleReservationStatus.BLOCKED,
  ]),
});

// Schema for reservation number availability check
export const checkVehicleReservationNumberSchema = z.object({
  reservationNumber: z.string().min(1, "Reservation number is required"),
});

// Form schema for UI components (includes additional fields for form handling)
export const vehicleReservationFormSchema = createVehicleReservationSchema;

// Type exports for use in components
export type CreateVehicleReservationSchema = z.infer<
  typeof createVehicleReservationSchema
>;
export type UpdateVehicleReservationSchema = z.infer<
  typeof updateVehicleReservationSchema
>;
export type GetVehicleReservationsSchema = z.infer<
  typeof getVehicleReservationsSchema
>;
export type BulkCreateVehicleReservationsSchema = z.infer<
  typeof bulkCreateVehicleReservationsSchema
>;
export type BulkDeleteVehicleReservationsSchema = z.infer<
  typeof bulkDeleteVehicleReservationsSchema
>;
export type BulkUpdateVehicleReservationStatusSchema = z.infer<
  typeof bulkUpdateVehicleReservationStatusSchema
>;
export type CheckVehicleReservationNumberSchema = z.infer<
  typeof checkVehicleReservationNumberSchema
>;
export type VehicleReservationFormSchema = z.infer<
  typeof vehicleReservationFormSchema
>;
