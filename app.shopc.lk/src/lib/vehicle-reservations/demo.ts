import { faker } from "@faker-js/faker";
import {
  VehicleReservationDto,
  VehicleReservationListDto,
  VehicleReservationSlimDto,
  VehicleReservationTableData,
  CreateVehicleReservationDto,
  UpdateVehicleReservationDto,
  BulkCreateVehicleReservationDto,
  VehicleReservationStatus,
  VehiclePickupLocationType,
  ReservationSource,
  PaymentStatus,
  DiscountType,
  TaxType,
  VehicleReservationPaginatedResponse,
  VehicleReservationIdResponse,
  BulkVehicleReservationIdsResponse,
  BulkDeleteVehicleReservationResponse,
  VehicleReservationNumberAvailabilityResponse,
  SimpleVehicleReservationResponse,
  VehicleReservationResponse,
  BulkUpdateVehicleReservationStatusResponse,
  DeleteVehicleReservationResponse,
  VehicleReservationVehicleDto,
  CreateVehicleReservationVehicleDto,
} from "@/types/vehicle-reservation";
import { ApiStatus } from "@/types/common";
import { GetVehicleReservationsSchema } from "./validations";

// Generate a UUID
function generateUUID(): string {
  return faker.string.uuid();
}

// Array to store demo vehicle reservations
let demoVehicleReservations: VehicleReservationDto[] = [];

// Helper function to generate a random reservation status
function generateRandomStatus(): VehicleReservationStatus {
  const statuses: VehicleReservationStatus[] = [
    VehicleReservationStatus.INQUIRY,
    VehicleReservationStatus.PENDING,
    VehicleReservationStatus.CONFIRMED,
    VehicleReservationStatus.PICKED_UP,
    VehicleReservationStatus.RETURNED,
    VehicleReservationStatus.CANCELLED,
    VehicleReservationStatus.NO_SHOW,
    VehicleReservationStatus.BLOCKED,
  ];
  return faker.helpers.arrayElement(statuses);
}

// Helper function to generate a random pickup location type
function generateRandomPickupLocationType(): VehiclePickupLocationType {
  const types: VehiclePickupLocationType[] = [
    VehiclePickupLocationType.OFFICE,
    VehiclePickupLocationType.AIRPORT,
    VehiclePickupLocationType.ADDRESS,
  ];
  return faker.helpers.arrayElement(types);
}

// Helper function to generate a random reservation source
function generateRandomReservationSource(): ReservationSource {
  const sources: ReservationSource[] = [
    ReservationSource.ONLINE,
    ReservationSource.PHONE,
    ReservationSource.WALK_IN,
    ReservationSource.EMAIL,
    ReservationSource.AGENT,
    ReservationSource.CORPORATE,
    ReservationSource.REPEAT_GUEST,
    ReservationSource.REFERRAL,
    ReservationSource.OTHER,
  ];
  return faker.helpers.arrayElement(sources);
}

// Helper function to generate a random payment status
function generateRandomPaymentStatus(): PaymentStatus {
  const statuses: PaymentStatus[] = [
    PaymentStatus.PENDING,
    PaymentStatus.PARTIAL,
    PaymentStatus.PAID,
    PaymentStatus.REFUNDED,
    PaymentStatus.CANCELLED,
  ];
  return faker.helpers.arrayElement(statuses);
}

// Helper function to generate a random discount type
function generateRandomDiscountType(): DiscountType {
  const types: DiscountType[] = [DiscountType.PERCENTAGE, DiscountType.FIXED];
  return faker.helpers.arrayElement(types);
}

// Helper function to generate a random tax type
function generateRandomTaxType(): TaxType {
  const types: TaxType[] = [TaxType.PERCENTAGE, TaxType.FIXED];
  return faker.helpers.arrayElement(types);
}

// Generate a random location
function generateRandomLocation(): string {
  const locations = [
    "Downtown Office",
    "Airport Terminal 1",
    "Airport Terminal 2",
    "Hotel Pickup",
    "Train Station",
    "Mall Parking",
    "City Center",
    "Suburban Office",
  ];
  return faker.helpers.arrayElement(locations);
}

// Generate a vehicle name
function generateVehicleName(): string {
  const make = faker.vehicle.manufacturer();
  const model = faker.vehicle.model();
  return `${make} ${model}`;
}

// Generate a random vehicle number
function generateVehicleNumber(): string {
  return faker.vehicle.vrm();
}

// Helper function to generate a valid pickup and return date
function generateDateRange(
  startDate: Date = new Date(),
  minDays: number = 1,
  maxDays: number = 30
): { pickUpDate: string; returnDate: string; totalDays: number } {
  const pickupDate = faker.date.between({
    from: startDate,
    to: new Date(startDate.getTime() + 90 * 24 * 60 * 60 * 1000),
  });

  const rentalDays = faker.number.int({ min: minDays, max: maxDays });
  const returnDate = new Date(
    pickupDate.getTime() + rentalDays * 24 * 60 * 60 * 1000
  );

  return {
    pickUpDate: pickupDate.toISOString(),
    returnDate: returnDate.toISOString(),
    totalDays: rentalDays,
  };
}

// Generate a demo vehicle reservation vehicle
function generateDemoVehicleReservationVehicle(): VehicleReservationVehicleDto {
  const vehicleId = generateUUID();
  const dailyRate = faker.number.float({
    min: 30,
    max: 300,
    fractionDigits: 2,
  });
  const discountAmount = faker.datatype.boolean()
    ? faker.number.float({ min: 5, max: 50, fractionDigits: 2 })
    : undefined;
  const taxRate = faker.datatype.boolean()
    ? faker.number.float({ min: 5, max: 15, fractionDigits: 2 })
    : undefined;

  return {
    id: generateUUID(),
    vehicleId,
    vehicle: {
      id: vehicleId,
      vehicleNumber: generateVehicleNumber(),
      make: faker.vehicle.manufacturer(),
      model: faker.vehicle.model(),
      year: faker.number.int({ min: 2015, max: 2024 }),
      color: faker.vehicle.color(),
    },
    vehicleDailyRate: dailyRate.toString(),
    subtotal: (dailyRate * faker.number.int({ min: 1, max: 7 })).toString(),
    total: (
      dailyRate * faker.number.int({ min: 1, max: 7 }) +
      (taxRate || 0)
    ).toString(),
    vehicleDiscountType: discountAmount
      ? generateRandomDiscountType()
      : undefined,
    vehicleDiscountAmount: discountAmount?.toString(),
    vehicleDiscountReason: discountAmount ? faker.lorem.sentence() : undefined,
    vehicleWithDriver: faker.datatype.boolean(),
    vehicleDriverId: faker.datatype.boolean() ? generateUUID() : undefined,
    vehicleDriverNotes: faker.datatype.boolean()
      ? faker.lorem.sentence()
      : undefined,
    vehicleExternalDriverName: faker.datatype.boolean()
      ? faker.person.fullName()
      : undefined,
    vehicleExternalDriverPhone: faker.datatype.boolean()
      ? faker.phone.number()
      : undefined,
    vehicleExternalDriverLicense: faker.datatype.boolean()
      ? faker.string.alphanumeric(10)
      : undefined,
    vehicleOrder: faker.number.int({ min: 1, max: 5 }),
    vehicleTaxRateId: taxRate ? generateUUID() : undefined,
    vehicleTaxRate: taxRate
      ? {
          id: generateUUID(),
          name: "VAT",
          rate: taxRate.toString(),
          type: generateRandomTaxType(),
        }
      : undefined,
    pickUpOdometer: faker.number.int({ min: 10000, max: 200000 }),
    returnOdometer: faker.number.int({ min: 10000, max: 200000 }),
    pickUpFuelLevel: faker.number.int({ min: 0, max: 100 }),
    returnFuelLevel: faker.number.int({ min: 0, max: 100 }),
    pickUpConditionNotes: faker.datatype.boolean()
      ? faker.lorem.sentence()
      : undefined,
    returnConditionNotes: faker.datatype.boolean()
      ? faker.lorem.sentence()
      : undefined,
    notes: faker.datatype.boolean() ? faker.lorem.sentence() : undefined,
  };
}

// Generate a demo vehicle reservation
function generateDemoVehicleReservation(): VehicleReservationDto {
  const id = generateUUID();
  const customerId = generateUUID();
  const { pickUpDate, returnDate } = generateDateRange();
  const status = generateRandomStatus();
  const paymentStatus = generateRandomPaymentStatus();
  const dailyRate = faker.number.float({
    min: 30,
    max: 300,
    fractionDigits: 2,
  });
  const discountAmount = faker.datatype.boolean()
    ? faker.number.float({ min: 5, max: 50, fractionDigits: 2 })
    : undefined;
  const taxRate = faker.datatype.boolean()
    ? faker.number.float({ min: 5, max: 15, fractionDigits: 2 })
    : undefined;
  const subtotal = dailyRate * faker.number.int({ min: 1, max: 7 });
  const total = subtotal + (taxRate || 0) - (discountAmount || 0);
  const totalPaid =
    paymentStatus === PaymentStatus.PAID
      ? total
      : paymentStatus === PaymentStatus.PARTIAL
      ? total * 0.5
      : 0;

  return {
    id,
    businessId: generateUUID(),
    reservationNumber: `VR-${faker.string.numeric(6)}`,
    referenceNumber: faker.datatype.boolean()
      ? `REF-${faker.string.alphanumeric(8)}`
      : undefined,
    customerId,
    customer: {
      id: customerId,
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
    },
    pickUpDate,
    returnDate,
    actualPickUpTime:
      status === VehicleReservationStatus.PICKED_UP ||
      status === VehicleReservationStatus.RETURNED
        ? faker.date.recent().toISOString()
        : undefined,
    actualReturnTime:
      status === VehicleReservationStatus.RETURNED
        ? faker.date.recent().toISOString()
        : undefined,
    status,
    reservationSource: generateRandomReservationSource(),
    dailyRate: dailyRate.toString(),
    discountType: discountAmount ? generateRandomDiscountType() : undefined,
    discountAmount: discountAmount?.toString(),
    discountReason: discountAmount ? faker.lorem.sentence() : undefined,
    subtotal: subtotal.toString(),
    total: total.toString(),
    paymentStatus,
    totalPaid: totalPaid.toString(),
    totalRefunded: "0.00",
    outstandingBalance: (total - totalPaid).toString(),
    taxRateId: taxRate ? generateUUID() : undefined,
    taxRate: taxRate
      ? {
          id: generateUUID(),
          name: "VAT",
          rate: taxRate.toString(),
          type: generateRandomTaxType(),
        }
      : undefined,
    pickUpLocationType: generateRandomPickupLocationType(),
    pickUpLocationId: faker.datatype.boolean() ? generateUUID() : undefined,
    pickUpAddress: faker.datatype.boolean()
      ? faker.location.streetAddress()
      : undefined,
    pickUpInstructions: faker.datatype.boolean()
      ? faker.lorem.sentence()
      : undefined,
    returnLocationType: generateRandomPickupLocationType(),
    returnLocationId: faker.datatype.boolean() ? generateUUID() : undefined,
    returnAddress: faker.datatype.boolean()
      ? faker.location.streetAddress()
      : undefined,
    returnInstructions: faker.datatype.boolean()
      ? faker.lorem.sentence()
      : undefined,
    flightArrivalDate: faker.datatype.boolean()
      ? faker.date.future().toISOString().split("T")[0]
      : undefined,
    flightArrivalTime: faker.datatype.boolean()
      ? faker.date.future().toTimeString().split(" ")[0].substring(0, 5)
      : undefined,
    flightDepartureDate: faker.datatype.boolean()
      ? faker.date.future().toISOString().split("T")[0]
      : undefined,
    flightDepartureTime: faker.datatype.boolean()
      ? faker.date.future().toTimeString().split(" ")[0].substring(0, 5)
      : undefined,
    arrivalFlightNumber: faker.datatype.boolean()
      ? faker.airline.flightNumber()
      : undefined,
    departureFlightNumber: faker.datatype.boolean()
      ? faker.airline.flightNumber()
      : undefined,
    arrivalAirline: faker.datatype.boolean()
      ? faker.airline.airline().name
      : undefined,
    departureAirline: faker.datatype.boolean()
      ? faker.airline.airline().name
      : undefined,
    cancellationReason:
      status === VehicleReservationStatus.CANCELLED
        ? faker.lorem.sentence()
        : undefined,
    cancellationDate:
      status === VehicleReservationStatus.CANCELLED
        ? faker.date.recent().toISOString()
        : undefined,
    notes: faker.datatype.boolean() ? faker.lorem.sentence() : undefined,
    confirmationSent: faker.datatype.boolean(),
    confirmationSentAt: faker.datatype.boolean()
      ? faker.date.recent().toISOString()
      : undefined,
    reminderSent: faker.datatype.boolean(),
    reminderSentAt: faker.datatype.boolean()
      ? faker.date.recent().toISOString()
      : undefined,
    vehicles: [generateDemoVehicleReservationVehicle()],
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    createdBy: {
      id: generateUUID(),
      name: faker.person.fullName(),
      avatar: faker.image.avatar(),
    },
    updatedBy: {
      id: generateUUID(),
      name: faker.person.fullName(),
      avatar: faker.image.avatar(),
    },
  };
}

// Initialize demo data with random reservations
function initializeDemoVehicleReservations() {
  if (demoVehicleReservations.length === 0) {
    // Generate between 20-50 random reservations
    const count = faker.number.int({ min: 20, max: 50 });
    for (let i = 0; i < count; i++) {
      demoVehicleReservations.push(generateDemoVehicleReservation());
    }
  }
}

// Convert VehicleReservationDto to VehicleReservationTableData
function convertToTableData(
  reservation: VehicleReservationDto
): VehicleReservationTableData {
  return {
    id: reservation.id,
    reservationNumber: reservation.reservationNumber,
    referenceNumber: reservation.referenceNumber,
    customerName: reservation.customer
      ? `${reservation.customer.firstName} ${reservation.customer.lastName}`
      : undefined,
    customerId: reservation.customerId,
    pickUpDate: reservation.pickUpDate,
    returnDate: reservation.returnDate,
    status: reservation.status,
    paymentStatus: reservation.paymentStatus,
    total: reservation.total,
    totalPaid: reservation.totalPaid,
    outstandingBalance: reservation.outstandingBalance,
    vehicleCount: reservation.vehicles.length,
    reservationSource: reservation.reservationSource,
    createdAt: reservation.createdAt,
    updatedAt: reservation.updatedAt,
  };
}

// Convert VehicleReservationDto to VehicleReservationListDto
function convertToListDto(
  reservation: VehicleReservationDto
): VehicleReservationListDto {
  return {
    id: reservation.id,
    reservationNumber: reservation.reservationNumber,
    referenceNumber: reservation.referenceNumber,
    customerId: reservation.customerId,
    customer: reservation.customer,
    pickUpDate: reservation.pickUpDate,
    returnDate: reservation.returnDate,
    status: reservation.status,
    reservationSource: reservation.reservationSource,
    paymentStatus: reservation.paymentStatus,
    total: reservation.total,
    totalPaid: reservation.totalPaid,
    outstandingBalance: reservation.outstandingBalance,
    vehicleCount: reservation.vehicles.length,
    createdAt: reservation.createdAt,
    updatedAt: reservation.updatedAt,
  };
}

// Convert VehicleReservationDto to VehicleReservationSlimDto
function convertToSlimDto(
  reservation: VehicleReservationDto
): VehicleReservationSlimDto {
  return {
    id: reservation.id,
    reservationNumber: reservation.reservationNumber,
    referenceNumber: reservation.referenceNumber,
    customerName: reservation.customer
      ? `${reservation.customer.firstName} ${reservation.customer.lastName}`
      : undefined,
    pickUpDate: reservation.pickUpDate,
    returnDate: reservation.returnDate,
    status: reservation.status,
    paymentStatus: reservation.paymentStatus,
    total: reservation.total,
    vehicleCount: reservation.vehicles.length,
    createdAt: reservation.createdAt,
  };
}

// Demo API: Get vehicle reservations with pagination and filtering
export async function getDemoVehicleReservationsTableDataApi(
  params: GetVehicleReservationsSchema & { isDemo?: boolean }
): Promise<VehicleReservationPaginatedResponse> {
  initializeDemoVehicleReservations();

  let filteredReservations = [...demoVehicleReservations];

  // Apply filters
  if (params.reservationNumber) {
    filteredReservations = filteredReservations.filter((r) =>
      r.reservationNumber
        .toLowerCase()
        .includes(params.reservationNumber!.toLowerCase())
    );
  }

  if (params.referenceNumber) {
    filteredReservations = filteredReservations.filter((r) =>
      r.referenceNumber
        ?.toLowerCase()
        .includes(params.referenceNumber!.toLowerCase())
    );
  }

  if (params.status) {
    filteredReservations = filteredReservations.filter(
      (r) => r.status === params.status
    );
  }

  if (params.paymentStatus) {
    filteredReservations = filteredReservations.filter(
      (r) => r.paymentStatus === params.paymentStatus
    );
  }

  if (params.customerId) {
    filteredReservations = filteredReservations.filter(
      (r) => r.customerId === params.customerId
    );
  }

  if (params.from) {
    filteredReservations = filteredReservations.filter(
      (r) => new Date(r.pickUpDate) >= new Date(params.from!)
    );
  }

  if (params.to) {
    filteredReservations = filteredReservations.filter(
      (r) => new Date(r.returnDate) <= new Date(params.to!)
    );
  }

  // Apply sorting
  if (params.sort) {
    const [field, direction] = params.sort.split(":");
    filteredReservations.sort((a, b) => {
      let aValue: any = a[field as keyof VehicleReservationDto];
      let bValue: any = b[field as keyof VehicleReservationDto];

      if (typeof aValue === "string") aValue = aValue.toLowerCase();
      if (typeof bValue === "string") bValue = bValue.toLowerCase();

      if (direction === "desc") {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });
  }

  // Apply pagination
  const page = params.page || 1;
  const limit = params.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedReservations = filteredReservations.slice(
    startIndex,
    endIndex
  );

  const totalPages = Math.ceil(filteredReservations.length / limit);

  return {
    status: ApiStatus.SUCCESS,
    message: "Vehicle reservations retrieved successfully",
    data: {
      data: paginatedReservations.map(convertToListDto),
      total: filteredReservations.length,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    },
  };
}

// Demo API: Get vehicle reservations in slim format
export async function getDemoVehicleReservationsSlimApi(): Promise<SimpleVehicleReservationResponse> {
  initializeDemoVehicleReservations();

  return {
    status: ApiStatus.SUCCESS,
    message: "Vehicle reservations retrieved successfully",
    data: demoVehicleReservations.map(convertToSlimDto),
  };
}

// Demo API: Get a single vehicle reservation by ID
export async function getDemoVehicleReservationApi(
  id: string
): Promise<VehicleReservationResponse> {
  initializeDemoVehicleReservations();

  const reservation = demoVehicleReservations.find((r) => r.id === id);

  if (!reservation) {
    return {
      status: ApiStatus.FAIL,
      message: "Vehicle reservation not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Vehicle reservation retrieved successfully",
    data: reservation,
  };
}

// Demo API: Check vehicle reservation number availability
export async function checkDemoVehicleReservationNumberAvailabilityApi(
  reservationNumber: string
): Promise<VehicleReservationNumberAvailabilityResponse> {
  initializeDemoVehicleReservations();

  const exists = demoVehicleReservations.some(
    (r) => r.reservationNumber === reservationNumber
  );

  return {
    status: ApiStatus.SUCCESS,
    message: exists
      ? "Reservation number is already taken"
      : "Reservation number is available",
    data: {
      available: !exists,
      reservationNumber,
      message: exists
        ? "Reservation number is already taken"
        : "Reservation number is available",
    },
  };
}

// Demo API: Create a vehicle reservation
export async function createDemoVehicleReservationApi(
  data: CreateVehicleReservationDto
): Promise<VehicleReservationIdResponse> {
  initializeDemoVehicleReservations();

  // Check if reservation number already exists
  const exists = demoVehicleReservations.some(
    (r) => r.reservationNumber === data.reservationNumber
  );
  if (exists) {
    return {
      status: ApiStatus.FAIL,
      message: "Reservation number already exists",
      data: null,
    };
  }

  const newReservation = generateDemoVehicleReservation();
  // Override with provided data
  Object.assign(newReservation, data);
  newReservation.id = generateUUID();
  newReservation.createdAt = new Date().toISOString();
  newReservation.updatedAt = new Date().toISOString();

  demoVehicleReservations.push(newReservation);

  return {
    status: ApiStatus.SUCCESS,
    message: "Vehicle reservation created successfully",
    data: { id: newReservation.id },
  };
}

// Demo API: Bulk create vehicle reservations
export async function bulkCreateDemoVehicleReservationsApi(
  reservations: BulkCreateVehicleReservationDto
): Promise<BulkVehicleReservationIdsResponse> {
  initializeDemoVehicleReservations();

  const createdIds: string[] = [];
  const failed: Array<{
    reservation: CreateVehicleReservationDto;
    error: string;
  }> = [];

  for (const reservationData of reservations.reservations) {
    // Check if reservation number already exists
    const exists = demoVehicleReservations.some(
      (r) => r.reservationNumber === reservationData.reservationNumber
    );
    if (exists) {
      failed.push({
        reservation: reservationData,
        error: "Reservation number already exists",
      });
      continue;
    }

    const newReservation = generateDemoVehicleReservation();
    Object.assign(newReservation, reservationData);
    newReservation.id = generateUUID();
    newReservation.createdAt = new Date().toISOString();
    newReservation.updatedAt = new Date().toISOString();

    demoVehicleReservations.push(newReservation);
    createdIds.push(newReservation.id);
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${createdIds.length} vehicle reservations created successfully`,
    data: {
      ids: createdIds,
      created: createdIds.length,
      message: `${createdIds.length} vehicle reservations created successfully`,
      failed: failed.length > 0 ? failed : undefined,
    },
  };
}

// Demo API: Update a vehicle reservation
export async function updateDemoVehicleReservationApi(
  id: string,
  data: UpdateVehicleReservationDto
): Promise<VehicleReservationIdResponse> {
  initializeDemoVehicleReservations();

  const index = demoVehicleReservations.findIndex((r) => r.id === id);
  if (index === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Vehicle reservation not found",
      data: null,
    };
  }

  // Update the reservation
  Object.assign(demoVehicleReservations[index], data);
  demoVehicleReservations[index].updatedAt = new Date().toISOString();

  return {
    status: ApiStatus.SUCCESS,
    message: "Vehicle reservation updated successfully",
    data: { id },
  };
}

// Demo API: Delete a vehicle reservation
export async function deleteDemoVehicleReservationApi(
  id: string
): Promise<DeleteVehicleReservationResponse> {
  initializeDemoVehicleReservations();

  const index = demoVehicleReservations.findIndex((r) => r.id === id);
  if (index === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Vehicle reservation not found",
      data: null,
    };
  }

  demoVehicleReservations.splice(index, 1);

  return {
    status: ApiStatus.SUCCESS,
    message: "Vehicle reservation deleted successfully",
    data: {
      success: true,
      message: "Vehicle reservation deleted successfully",
    },
  };
}

// Demo API: Bulk delete vehicle reservations
export async function bulkDeleteDemoVehicleReservationsApi(data: {
  vehicleReservationIds: string[];
}): Promise<BulkDeleteVehicleReservationResponse> {
  initializeDemoVehicleReservations();

  const deletedIds: string[] = [];
  const failed: Array<{ id: string; error: string }> = [];

  for (const id of data.vehicleReservationIds) {
    const index = demoVehicleReservations.findIndex((r) => r.id === id);
    if (index === -1) {
      failed.push({
        id,
        error: "Vehicle reservation not found",
      });
      continue;
    }

    demoVehicleReservations.splice(index, 1);
    deletedIds.push(id);
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${deletedIds.length} vehicle reservations deleted successfully`,
    data: {
      deleted: deletedIds.length,
      message: `${deletedIds.length} vehicle reservations deleted successfully`,
      deletedIds,
      failed: failed.length > 0 ? failed : undefined,
    },
  };
}

// Demo API: Update vehicle reservation status
export async function updateDemoVehicleReservationStatusApi(
  id: string,
  status: VehicleReservationStatus
): Promise<VehicleReservationIdResponse> {
  initializeDemoVehicleReservations();

  const index = demoVehicleReservations.findIndex((r) => r.id === id);
  if (index === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Vehicle reservation not found",
      data: null,
    };
  }

  demoVehicleReservations[index].status = status;
  demoVehicleReservations[index].updatedAt = new Date().toISOString();

  return {
    status: ApiStatus.SUCCESS,
    message: "Vehicle reservation status updated successfully",
    data: { id },
  };
}

// Demo API: Bulk update vehicle reservation status
export async function bulkUpdateDemoVehicleReservationStatusApi(data: {
  vehicleReservationIds: string[];
  status: VehicleReservationStatus;
}): Promise<BulkUpdateVehicleReservationStatusResponse> {
  initializeDemoVehicleReservations();

  const updatedIds: string[] = [];
  const failed: Array<{ id: string; error: string }> = [];

  for (const id of data.vehicleReservationIds) {
    const index = demoVehicleReservations.findIndex((r) => r.id === id);
    if (index === -1) {
      failed.push({
        id,
        error: "Vehicle reservation not found",
      });
      continue;
    }

    demoVehicleReservations[index].status = data.status;
    demoVehicleReservations[index].updatedAt = new Date().toISOString();
    updatedIds.push(id);
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${updatedIds.length} vehicle reservation statuses updated successfully`,
    data: {
      updated: updatedIds.length,
      message: `${updatedIds.length} vehicle reservation statuses updated successfully`,
      updatedIds,
      failed: failed.length > 0 ? failed : undefined,
    },
  };
}
