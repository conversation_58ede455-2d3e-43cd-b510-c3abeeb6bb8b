// Export all vehicle reservation related functions and types

// API functions
export {
  createVehicleReservationApi,
  bulkCreateVehicleReservationsApi,
  getVehicleReservationsApi,
  getVehicleReservationsSlimApi,
  getVehicleReservationApi,
  updateVehicleReservationApi,
  deleteVehicleReservationApi,
  bulkDeleteVehicleReservationsApi,
  bulkUpdateVehicleReservationStatusApi,
  checkVehicleReservationNumberAvailabilityApi,
} from "./api";

// Demo API functions
export {
  getDemoVehicleReservationsTableDataApi,
  getDemoVehicleReservationsSlimApi,
  getDemoVehicleReservationApi,
  checkDemoVehicleReservationNumberAvailabilityApi,
  createDemoVehicleReservationApi,
  bulkCreateDemoVehicleReservationsApi,
  updateDemoVehicleReservationApi,
  deleteDemoVehicleReservationApi,
  bulkDeleteDemoVehicleReservationsApi,
  updateDemoVehicleReservationStatus<PERSON>pi,
  bulkUpdateDemoVehicleReservationStatusApi,
} from "./demo";

// React hooks
export {
  vehicleReservationKeys,
  useVehicleReservations,
  useVehicleReservationsSlim,
  useVehicleReservation,
  useVehicleReservationNumberAvailability,
  useCreateVehicleReservation,
  useBulkCreateVehicleReservations,
  useUpdateVehicleReservation,
  useDeleteVehicleReservation,
  useBulkDeleteVehicleReservations,
  useUpdateVehicleReservationStatus,
  useBulkUpdateVehicleReservationStatus,
} from "./hooks";

// Query functions
export {
  getVehicleReservationsTableData,
  getVehicleReservationsSlim,
  getVehicleReservation,
  checkVehicleReservationNumberAvailability,
  createVehicleReservation,
  bulkCreateVehicleReservations,
  updateVehicleReservation,
  deleteVehicleReservation,
  bulkDeleteVehicleReservations,
  updateVehicleReservationStatus,
  bulkUpdateVehicleReservationStatus,
} from "./queries";

// Validation schemas
export {
  createVehicleReservationVehicleSchema,
  createVehicleReservationSchema,
  updateVehicleReservationVehicleSchema,
  updateVehicleReservationSchema,
  getVehicleReservationsSchema,
  bulkCreateVehicleReservationsSchema,
  bulkDeleteVehicleReservationsSchema,
  bulkUpdateVehicleReservationStatusSchema,
  checkVehicleReservationNumberSchema,
  vehicleReservationFormSchema,
} from "./validations";

// Validation types
export type {
  CreateVehicleReservationSchema,
  UpdateVehicleReservationSchema,
  GetVehicleReservationsSchema,
  BulkCreateVehicleReservationsSchema,
  BulkDeleteVehicleReservationsSchema,
  BulkUpdateVehicleReservationStatusSchema,
  CheckVehicleReservationNumberSchema,
  VehicleReservationFormSchema,
} from "./validations";
