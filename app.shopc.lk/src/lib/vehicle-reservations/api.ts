import {
  VehicleReservationResponse,
  VehicleReservationPaginatedResponse,
  VehicleReservationIdResponse,
  BulkVehicleReservationIdsResponse,
  BulkDeleteVehicleReservationDto,
  BulkDeleteVehicleReservationResponse,
  VehicleReservationNumberAvailabilityResponse,
  SimpleVehicleReservationResponse,
  CreateVehicleReservationDto,
  UpdateVehicleReservationDto,
  BulkCreateVehicleReservationDto,
  VehicleReservationStatus,
  BulkUpdateVehicleReservationStatusDto,
  BulkUpdateVehicleReservationStatusResponse,
  DeleteVehicleReservationResponse,
} from "@/types/vehicle-reservation";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetVehicleReservationsSchema } from "./validations";
import axios from "@/utils/axios";

// Create a new vehicle reservation
export async function createVehicleReservationApi(
  data: CreateVehicleReservationDto
): Promise<VehicleReservationIdResponse> {
  try {
    const res = await axios.post("/vehicle-reservations", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create vehicle reservations
export async function bulkCreateVehicleReservationsApi(
  reservations: BulkCreateVehicleReservationDto
): Promise<BulkVehicleReservationIdsResponse> {
  try {
    const res = await axios.post("/vehicle-reservations/bulk", reservations);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get vehicle reservations with pagination and filtering
export async function getVehicleReservationsApi(
  params: GetVehicleReservationsSchema
): Promise<VehicleReservationPaginatedResponse> {
  try {
    const res = await axios.get("/vehicle-reservations", { params });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get vehicle reservations in slim format (for dropdowns/selects)
export async function getVehicleReservationsSlimApi(): Promise<SimpleVehicleReservationResponse> {
  try {
    const res = await axios.get("/vehicle-reservations/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single vehicle reservation by ID
export async function getVehicleReservationApi(
  id: string
): Promise<VehicleReservationResponse> {
  try {
    const res = await axios.get(`/vehicle-reservations/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update a vehicle reservation
export async function updateVehicleReservationApi(
  id: string,
  data: UpdateVehicleReservationDto
): Promise<VehicleReservationIdResponse> {
  try {
    const res = await axios.patch(`/vehicle-reservations/${id}`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete a vehicle reservation
export async function deleteVehicleReservationApi(
  id: string
): Promise<DeleteVehicleReservationResponse> {
  try {
    const res = await axios.delete(`/vehicle-reservations/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete vehicle reservations
export async function bulkDeleteVehicleReservationsApi(
  data: BulkDeleteVehicleReservationDto
): Promise<BulkDeleteVehicleReservationResponse> {
  try {
    const res = await axios.delete("/vehicle-reservations/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk update vehicle reservation status
export async function bulkUpdateVehicleReservationStatusApi(
  data: BulkUpdateVehicleReservationStatusDto
): Promise<BulkUpdateVehicleReservationStatusResponse> {
  try {
    const res = await axios.patch("/vehicle-reservations/bulk-status", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check vehicle reservation number availability
export async function checkVehicleReservationNumberAvailabilityApi(
  reservationNumber: string
): Promise<VehicleReservationNumberAvailabilityResponse> {
  try {
    const res = await axios.get(
      `/vehicle-reservations/check-reservation-number/${reservationNumber}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}
