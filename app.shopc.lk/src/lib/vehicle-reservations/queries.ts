import {
  VehicleReservationPaginatedResponse,
  VehicleReservationIdResponse,
  BulkVehicleReservationIdsResponse,
  BulkDeleteVehicleReservationResponse,
  VehicleReservationNumberAvailabilityResponse,
  SimpleVehicleReservationResponse,
  VehicleReservationResponse,
  CreateVehicleReservationDto,
  UpdateVehicleReservationDto,
  BulkCreateVehicleReservationDto,
  VehicleReservationStatus,
  BulkUpdateVehicleReservationStatusDto,
  BulkUpdateVehicleReservationStatusResponse,
  DeleteVehicleReservationResponse,
  VehicleReservationTableData,
} from "@/types/vehicle-reservation";
import { ApiResponse, ApiStatus } from "@/types/common";
import {
  GetVehicleReservationsSchema,
  CreateVehicleReservationSchema,
  UpdateVehicleReservationSchema,
  BulkCreateVehicleReservationsSchema,
  CheckVehicleReservationNumberSchema,
} from "./validations";

// Real API imports
import {
  createVehicleReservationApi,
  bulkCreateVehicleReservationsApi,
  getVehicleReservationsApi,
  checkVehicleReservationNumberAvailabilityApi,
  getVehicleReservationsSlimApi,
  getVehicleReservationApi,
  updateVehicleReservationApi,
  deleteVehicleReservationApi,
  bulkDeleteVehicleReservationsApi,
  bulkUpdateVehicleReservationStatusApi,
} from "./api";

// Demo API imports
import {
  getDemoVehicleReservationsTableDataApi,
  getDemoVehicleReservationsSlimApi,
  getDemoVehicleReservationApi,
  checkDemoVehicleReservationNumberAvailabilityApi,
  createDemoVehicleReservationApi,
  bulkCreateDemoVehicleReservationsApi,
  updateDemoVehicleReservationApi,
  deleteDemoVehicleReservationApi,
  bulkDeleteDemoVehicleReservationsApi,
  updateDemoVehicleReservationStatusApi,
  bulkUpdateDemoVehicleReservationStatusApi,
} from "./demo";

// Get vehicle reservations with pagination and filtering
export async function getVehicleReservationsTableData(
  params: GetVehicleReservationsSchema & { isDemo?: boolean },
  isDemo: boolean
): Promise<VehicleReservationPaginatedResponse> {
  if (isDemo) {
    return await getDemoVehicleReservationsTableDataApi(params);
  } else {
    return await getVehicleReservationsApi(params);
  }
}

// Get vehicle reservations in slim format
export async function getVehicleReservationsSlim(
  isDemo: boolean
): Promise<SimpleVehicleReservationResponse> {
  if (isDemo) {
    return await getDemoVehicleReservationsSlimApi();
  } else {
    return await getVehicleReservationsSlimApi();
  }
}

// Get a single vehicle reservation by ID
export async function getVehicleReservation(
  id: string,
  isDemo: boolean
): Promise<VehicleReservationResponse> {
  if (isDemo) {
    return await getDemoVehicleReservationApi(id);
  } else {
    return await getVehicleReservationApi(id);
  }
}

// Check vehicle reservation number availability
export async function checkVehicleReservationNumberAvailability(
  reservationNumber: string,
  isDemo: boolean
): Promise<VehicleReservationNumberAvailabilityResponse> {
  if (isDemo) {
    return await checkDemoVehicleReservationNumberAvailabilityApi(
      reservationNumber
    );
  } else {
    return await checkVehicleReservationNumberAvailabilityApi(
      reservationNumber
    );
  }
}

// Create a new vehicle reservation
export async function createVehicleReservation(
  data: CreateVehicleReservationDto,
  isDemo: boolean
): Promise<VehicleReservationIdResponse> {
  if (isDemo) {
    return await createDemoVehicleReservationApi(data);
  } else {
    return await createVehicleReservationApi(data);
  }
}

// Bulk create vehicle reservations
export async function bulkCreateVehicleReservations(
  reservations: BulkCreateVehicleReservationDto,
  isDemo: boolean
): Promise<BulkVehicleReservationIdsResponse> {
  if (isDemo) {
    return await bulkCreateDemoVehicleReservationsApi(reservations);
  } else {
    return await bulkCreateVehicleReservationsApi(reservations);
  }
}

// Update a vehicle reservation
export async function updateVehicleReservation(
  id: string,
  data: UpdateVehicleReservationDto,
  isDemo: boolean
): Promise<VehicleReservationIdResponse> {
  if (isDemo) {
    return await updateDemoVehicleReservationApi(id, data);
  } else {
    return await updateVehicleReservationApi(id, data);
  }
}

// Delete a vehicle reservation
export async function deleteVehicleReservation(
  id: string,
  isDemo: boolean
): Promise<DeleteVehicleReservationResponse> {
  if (isDemo) {
    return await deleteDemoVehicleReservationApi(id);
  } else {
    return await deleteVehicleReservationApi(id);
  }
}

// Bulk delete vehicle reservations
export async function bulkDeleteVehicleReservations(
  data: { vehicleReservationIds: string[] },
  isDemo: boolean
): Promise<BulkDeleteVehicleReservationResponse> {
  if (isDemo) {
    return await bulkDeleteDemoVehicleReservationsApi(data);
  } else {
    return await bulkDeleteVehicleReservationsApi(data);
  }
}

// Update vehicle reservation status
export async function updateVehicleReservationStatus(
  id: string,
  status: VehicleReservationStatus,
  isDemo: boolean
): Promise<VehicleReservationIdResponse> {
  if (isDemo) {
    return await updateDemoVehicleReservationStatusApi(id, status);
  } else {
    // Note: This endpoint doesn't exist in the backend controller, so we'll use the update endpoint
    return await updateVehicleReservationApi(id, { status });
  }
}

// Bulk update vehicle reservation status
export async function bulkUpdateVehicleReservationStatus(
  data: BulkUpdateVehicleReservationStatusDto,
  isDemo: boolean
): Promise<BulkUpdateVehicleReservationStatusResponse> {
  if (isDemo) {
    return await bulkUpdateDemoVehicleReservationStatusApi(data);
  } else {
    return await bulkUpdateVehicleReservationStatusApi(data);
  }
}
