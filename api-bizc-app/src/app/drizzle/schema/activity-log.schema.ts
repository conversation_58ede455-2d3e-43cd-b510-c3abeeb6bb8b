import {
  pgTable,
  pgEnum,
  serial,
  varchar,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { ActivityLogName } from '../../shared/types';

export const activityLogNameEnum = pgEnum('activity_log_name', [
  ActivityLogName.LOGIN,
  ActivityLogName.LOGOUT,
  ActivityLogName.CREATE,
  ActivityLogName.UPDATE,
  ActivityLogName.DELETE,
  ActivityLogName.VIEW,
  ActivityLogName.OTHER,
  ActivityLogName.MEAL_PERIOD_CREATED,
  ActivityLogName.MEAL_PERIOD_UPDATED,
  ActivityLogName.MEAL_PERIOD_DELETED,
  ActivityLogName.CUSTOMER_GROUP_CREATED,
  ActivityLogName.CUSTOMER_GROUP_UPDATED,
  ActivityLogName.CUSTOMER_GROUP_DELETED,
  ActivityLogName.BUSINESS_ACCOUNTING_SETTINGS_UPDATED,
  ActivityLogName.PERFORMANCE_REVIEW_CREATED,
  ActivityLogName.PERFORMANCE_REVIEW_UPDATED,
  ActivityLogName.PERFORMANCE_REVIEW_DELETED,
  ActivityLogName.PERFORMANCE_REVIEW_BULK_DELETED,
  ActivityLogName.CREATE_TIME_SLOT,
  ActivityLogName.UPDATE_TIME_SLOT,
  ActivityLogName.DELETE_TIME_SLOT,
  ActivityLogName.TABLE_RESERVATION_CREATED,
  ActivityLogName.TABLE_RESERVATION_UPDATED,
  ActivityLogName.TABLE_RESERVATION_DELETED,
  ActivityLogName.TABLE_RESERVATION_STATUS_UPDATED,
  ActivityLogName.TABLE_RESERVATION_CANCELLED,
  ActivityLogName.TABLE_RESERVATION_CONFIRMED,
  ActivityLogName.TABLE_RESERVATION_COMPLETED,
  ActivityLogName.TABLE_RESERVATION_NO_SHOW,
  ActivityLogName.PACKAGE_CREATED,
  ActivityLogName.PACKAGE_UPDATED,
  ActivityLogName.PACKAGE_DELETED,
  ActivityLogName.PACKAGE_BULK_CREATED,
  ActivityLogName.PACKAGE_BULK_DELETED,
  ActivityLogName.PACKAGE_STATUS_UPDATED,
  ActivityLogName.PACKAGE_POSITIONS_UPDATED,
  ActivityLogName.WORK_ORDER_CREATED,
  ActivityLogName.WORK_ORDER_UPDATED,
  ActivityLogName.WORK_ORDER_DELETED,
  ActivityLogName.WORK_ORDER_BULK_CREATED,
  ActivityLogName.WORK_ORDER_BULK_DELETED,
  ActivityLogName.EVENT_SPACE_RESERVATION_CREATED,
  ActivityLogName.EVENT_SPACE_RESERVATION_UPDATED,
  ActivityLogName.EVENT_SPACE_RESERVATION_DELETED,
  ActivityLogName.EVENT_SPACE_RESERVATION_BULK_CREATED,
  ActivityLogName.EVENT_SPACE_RESERVATION_BULK_DELETED,
  ActivityLogName.EVENT_SPACE_RESERVATION_BULK_STATUS_UPDATED,
  ActivityLogName.VEHICLE_RESERVATION_CREATED,
  ActivityLogName.VEHICLE_RESERVATION_UPDATED,
  ActivityLogName.VEHICLE_RESERVATION_DELETED,
  ActivityLogName.VEHICLE_RESERVATION_BULK_CREATED,
  ActivityLogName.VEHICLE_RESERVATION_BULK_DELETED,
  ActivityLogName.VEHICLE_RESERVATION_BULK_STATUS_UPDATED,
]);

export const activityLog = pgTable('activity_log', {
  id: serial('id').primaryKey(),
  logName: varchar('log_name', { length: 191 }),
  description: text('description').notNull(),
  subjectId: uuid('subject_id'),
  subjectType: varchar('subject_type', { length: 191 }),
  causerId: uuid('causer_id'),
  causerType: varchar('causer_type', { length: 191 }),
  properties: text('properties'),
  createdAt: timestamp('created_at'),
  updatedAt: timestamp('updated_at'),
});
