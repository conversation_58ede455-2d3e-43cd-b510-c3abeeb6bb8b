import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  integer,
  decimal,
  boolean,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';
import { business } from './business.schema';
import { vehicles } from './vehicles.schema';
import { customers } from './customers.schema';
import { taxes } from './taxes.schema';
import { staffMembers } from './staff.schema';
import { auditFields } from './common-fields.schema';
import {
  PaymentStatus,
  TaxType,
  DiscountType,
} from '../../shared/types/common.enum';
import {
  VehicleReservationStatus,
  VehiclePickupLocationType,
} from '../../shared/types/vehicle.enum';
import { ReservationSource } from '../../shared/types/accommodation.enum';

// Vehicle Reservation Status Enum
export const vehicleReservationStatusEnum = pgEnum(
  'vehicle_reservation_status',
  [
    VehicleReservationStatus.INQUIRY,
    VehicleReservationStatus.PENDING,
    VehicleReservationStatus.CONFIRMED,
    VehicleReservationStatus.PICKED_UP,
    VehicleReservationStatus.RETURNED,
    VehicleReservationStatus.CANCELLED,
    VehicleReservationStatus.NO_SHOW,
    VehicleReservationStatus.BLOCKED,
  ],
);

// Payment Status Enum
export const paymentStatusEnum = pgEnum('payment_status', [
  PaymentStatus.PENDING,
  PaymentStatus.PARTIAL,
  PaymentStatus.PAID,
  PaymentStatus.REFUNDED,
  PaymentStatus.CANCELLED,
]);

// Vehicle Pickup Location Type Enum
export const vehiclePickupLocationTypeEnum = pgEnum(
  'vehicle_pickup_location_type',
  [
    VehiclePickupLocationType.OFFICE,
    VehiclePickupLocationType.AIRPORT,
    VehiclePickupLocationType.ADDRESS,
  ],
);

// Tax Type Enum
export const taxTypeEnum = pgEnum('tax_type', [
  TaxType.INCLUSIVE,
  TaxType.EXCLUSIVE,
  TaxType.OUT_OF_SCOPE,
]);

// Discount Type Enum
export const discountTypeEnum = pgEnum('discount_type', [
  DiscountType.PERCENTAGE,
  DiscountType.FIXED_AMOUNT,
]);

// Reservation Source Enum
export const reservationSourceEnum = pgEnum('reservation_source', [
  ReservationSource.ONLINE,
  ReservationSource.PHONE,
  ReservationSource.WALK_IN,
  ReservationSource.EMAIL,
  ReservationSource.AGENT,
  ReservationSource.CORPORATE,
  ReservationSource.REPEAT_GUEST,
  ReservationSource.REFERRAL,
  ReservationSource.OTHER,
]);

// Vehicle Reservations table
export const vehicleReservations = pgTable(
  'vehicle_reservations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Reservation identification
    reservationNumber: text('reservation_number').notNull(),
    referenceNumber: text('reference_number'), // External booking reference

    // Customer reference
    customerId: uuid('customer_id').references(() => customers.id),

    // Reservation dates and duration
    pickUpDate: timestamp('pick_up_date').notNull(),
    returnDate: timestamp('return_date').notNull(),
    actualPickUpTime: timestamp('actual_pick_up_time'),
    actualReturnTime: timestamp('actual_return_time'),

    // Reservation status and type
    status: vehicleReservationStatusEnum('status')
      .default(VehicleReservationStatus.PENDING)
      .notNull(),
    reservationSource: reservationSourceEnum('reservation_source'),

    // Pricing information
    dailyRate: decimal('daily_rate', {
      precision: 12,
      scale: 2,
    }).notNull(),
    subtotal: decimal('subtotal', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    total: decimal('total', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),

    // Discount information
    discountType: discountTypeEnum('discount_type'),
    discountValue: decimal('discount_value', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    discountAmount: decimal('discount_amount', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    discountReason: text('discount_reason'),

    // Payment information
    paymentStatus: paymentStatusEnum('payment_status')
      .default(PaymentStatus.PENDING)
      .notNull(),
    depositRequired: decimal('deposit_required', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    depositPaid: decimal('deposit_paid', { precision: 12, scale: 2 }).default(
      '0.00',
    ),
    balanceDue: decimal('balance_due', { precision: 12, scale: 2 }).default(
      '0.00',
    ),

    // Tax information
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),

    // Reservation metadata
    notes: text('notes'),
    cancellationReason: text('cancellation_reason'),
    cancellationDate: timestamp('cancellation_date'),

    // Enhanced Pick-up and return location details
    pickUpLocationType: vehiclePickupLocationTypeEnum('pick_up_location_type')
      .default(VehiclePickupLocationType.OFFICE)
      .notNull(),
    returnLocationType: vehiclePickupLocationTypeEnum('return_location_type')
      .default(VehiclePickupLocationType.OFFICE)
      .notNull(),
    pickUpLocation: text('pick_up_location'),
    returnLocation: text('return_location'),
    pickUpAddress: text('pick_up_address'), // Custom address for ADDRESS type
    returnAddress: text('return_address'), // Custom address for ADDRESS type
    pickUpInstructions: text('pick_up_instructions'),
    returnInstructions: text('return_instructions'),

    // Flight information for AIRPORT pickup/return
    flightArrivalDate: timestamp('flight_arrival_date'),
    flightArrivalTime: text('flight_arrival_time'), // HH:MM format
    flightDepartureDate: timestamp('flight_departure_date'),
    flightDepartureTime: text('flight_departure_time'), // HH:MM format
    arrivalFlightNumber: text('arrival_flight_number'),
    departureFlightNumber: text('departure_flight_number'),
    arrivalAirline: text('arrival_airline'),
    departureAirline: text('departure_airline'),

    // Confirmation and communication
    confirmationSent: boolean('confirmation_sent').default(false).notNull(),
    confirmationSentAt: timestamp('confirmation_sent_at'),
    reminderSent: boolean('reminder_sent').default(false).notNull(),
    reminderSentAt: timestamp('reminder_sent_at'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Primary indexes
    businessIdIndex: index('vehicle_reservations_business_id_index').on(
      t.businessId,
    ),
    reservationNumberIndex: index('vehicle_reservations_number_index').on(
      t.reservationNumber,
    ),
    referenceNumberIndex: index('vehicle_reservations_reference_index').on(
      t.referenceNumber,
    ),

    // Foreign key indexes
    customerIdIndex: index('vehicle_reservations_customer_id_index').on(
      t.customerId,
    ),

    // Date and time indexes for availability queries
    pickUpDateIndex: index('vehicle_reservations_pick_up_date_index').on(
      t.pickUpDate,
    ),
    returnDateIndex: index('vehicle_reservations_return_date_index').on(
      t.returnDate,
    ),
    actualPickUpTimeIndex: index(
      'vehicle_reservations_actual_pick_up_index',
    ).on(t.actualPickUpTime),
    actualReturnTimeIndex: index('vehicle_reservations_actual_return_index').on(
      t.actualReturnTime,
    ),

    // Status and type indexes
    statusIndex: index('vehicle_reservations_status_index').on(t.status),
    paymentStatusIndex: index('vehicle_reservations_payment_status_index').on(
      t.paymentStatus,
    ),
    reservationSourceIndex: index('vehicle_reservations_source_index').on(
      t.reservationSource,
    ),

    confirmationSentIndex: index(
      'vehicle_reservations_confirmation_sent_index',
    ).on(t.confirmationSent),

    // Pricing and discount indexes
    discountTypeIndex: index('vehicle_reservations_discount_type_index').on(
      t.discountType,
    ),
    subtotalIndex: index('vehicle_reservations_subtotal_index').on(t.subtotal),
    totalIndex: index('vehicle_reservations_total_index').on(t.total),

    // Location type indexes
    pickUpLocationTypeIndex: index(
      'vehicle_reservations_pickup_location_type_index',
    ).on(t.pickUpLocationType),
    returnLocationTypeIndex: index(
      'vehicle_reservations_return_location_type_index',
    ).on(t.returnLocationType),

    // Flight information indexes
    flightArrivalDateIndex: index(
      'vehicle_reservations_flight_arrival_date_index',
    ).on(t.flightArrivalDate),
    flightDepartureDateIndex: index(
      'vehicle_reservations_flight_departure_date_index',
    ).on(t.flightDepartureDate),
    arrivalFlightNumberIndex: index(
      'vehicle_reservations_arrival_flight_number_index',
    ).on(t.arrivalFlightNumber),
    departureFlightNumberIndex: index(
      'vehicle_reservations_departure_flight_number_index',
    ).on(t.departureFlightNumber),

    // Composite indexes for common queries
    businessStatusIndex: index('vehicle_reservations_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessDateRangeIndex: index(
      'vehicle_reservations_business_date_range_index',
    ).on(t.businessId, t.pickUpDate, t.returnDate),
    customerReservationsIndex: index('vehicle_reservations_customer_index').on(
      t.customerId,
      t.status,
      t.pickUpDate,
    ),
    paymentDueIndex: index('vehicle_reservations_payment_due_index').on(
      t.businessId,
      t.paymentStatus,
      t.pickUpDate,
    ),

    // Unique constraints with soft deletion support
    uniqueBusinessReservationNumber: uniqueIndex(
      'vehicle_reservations_business_number_unique',
    )
      .on(t.businessId, t.reservationNumber)
      .where(isNull(t.deletedAt)),
    uniqueBusinessReferenceNumber: uniqueIndex(
      'vehicle_reservations_business_reference_unique',
    )
      .on(t.businessId, t.referenceNumber)
      .where(isNull(t.deletedAt)),
  }),
);

// Junction table for reservation-vehicle relationships (many-to-many)
export const vehicleReservationVehicles = pgTable(
  'vehicle_reservation_vehicles',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    reservationId: uuid('reservation_id')
      .notNull()
      .references(() => vehicleReservations.id, {
        onDelete: 'cascade',
      }),
    vehicleId: uuid('vehicle_id')
      .notNull()
      .references(() => vehicles.id, {
        onDelete: 'cascade',
      }),

    // Vehicle-specific reservation details
    vehicleDailyRate: decimal('vehicle_daily_rate', {
      precision: 12,
      scale: 2,
    }).notNull(),
    subtotal: decimal('subtotal', {
      precision: 12,
      scale: 2,
    })
      .default('0.00')
      .notNull(),
    total: decimal('total', {
      precision: 12,
      scale: 2,
    }).notNull(),

    // Vehicle-specific discount information
    vehicleDiscountType: discountTypeEnum('vehicle_discount_type'),
    vehicleDiscountValue: decimal('vehicle_discount_value', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    vehicleDiscountAmount: decimal('vehicle_discount_amount', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    vehicleDiscountReason: text('vehicle_discount_reason'),

    // Vehicle-specific driver information
    vehicleWithDriver: boolean('vehicle_with_driver').default(false).notNull(),
    vehicleDriverId: uuid('vehicle_driver_id').references(
      () => staffMembers.id,
    ), // Reference to staff member
    vehicleDriverNotes: text('vehicle_driver_notes'), // Vehicle-specific driver requirements or preferences
    vehicleExternalDriverName: text('vehicle_external_driver_name'), // For external/customer-provided drivers
    vehicleExternalDriverLicense: text('vehicle_external_driver_license'),
    vehicleExternalDriverContact: text('vehicle_external_driver_contact'),

    vehiclePickUpTime: timestamp('vehicle_pick_up_time'),
    vehicleReturnTime: timestamp('vehicle_return_time'),

    // Vehicle condition and inspection
    pickUpOdometer: integer('pick_up_odometer'),
    returnOdometer: integer('return_odometer'),
    pickUpFuelLevel: integer('pick_up_fuel_level'), // 0-100 percentage
    returnFuelLevel: integer('return_fuel_level'), // 0-100 percentage
    pickUpConditionNotes: text('pick_up_condition_notes'),
    returnConditionNotes: text('return_condition_notes'),

    // Vehicle-specific preferences and notes
    notes: text('notes'), // Reservation-specific notes about this vehicle

    // Vehicle order in multi-vehicle reservation
    vehicleOrder: integer('vehicle_order').default(1).notNull(),

    vehicleTaxRateId: uuid('vehicle_tax_rate_id').references(() => taxes.id),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Primary indexes
    reservationIdIndex: index(
      'vehicle_reservation_vehicles_reservation_id_index',
    ).on(t.reservationId),
    vehicleIdIndex: index('vehicle_reservation_vehicles_vehicle_id_index').on(
      t.vehicleId,
    ),

    vehicleOrderIndex: index(
      'vehicle_reservation_vehicles_vehicle_order_index',
    ).on(t.vehicleOrder),

    // Time-based indexes
    vehiclePickUpTimeIndex: index(
      'vehicle_reservation_vehicles_pick_up_time_index',
    ).on(t.vehiclePickUpTime),
    vehicleReturnTimeIndex: index(
      'vehicle_reservation_vehicles_return_time_index',
    ).on(t.vehicleReturnTime),

    // Tax-related indexes
    vehicleTaxRateIdIndex: index(
      'vehicle_reservation_vehicles_vehicle_tax_rate_index',
    ).on(t.vehicleTaxRateId),

    // Vehicle-specific discount indexes
    vehicleDiscountTypeIndex: index(
      'vehicle_reservation_vehicles_vehicle_discount_type_index',
    ).on(t.vehicleDiscountType),

    // Vehicle-specific driver indexes
    vehicleWithDriverIndex: index(
      'vehicle_reservation_vehicles_vehicle_with_driver_index',
    ).on(t.vehicleWithDriver),
    vehicleDriverIdIndex: index(
      'vehicle_reservation_vehicles_vehicle_driver_id_index',
    ).on(t.vehicleDriverId),

    // Composite indexes for common queries
    reservationVehicleOrderIndex: index(
      'vehicle_reservation_vehicles_reservation_order_index',
    ).on(t.reservationId, t.vehicleOrder),

    // Availability check for individual vehicles
    vehicleDateOverlapIndex: index(
      'vehicle_reservation_vehicles_vehicle_overlap_check',
    )
      .on(t.vehicleId)
      .where(isNull(t.deletedAt)),

    // Unique constraints
    uniqueReservationVehicle: uniqueIndex(
      'vehicle_reservation_vehicles_unique',
    ).on(t.reservationId, t.vehicleId),
  }),
);
