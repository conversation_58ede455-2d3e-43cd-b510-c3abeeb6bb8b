import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BulkDeleteVehicleReservationResponseDto {
  @ApiProperty({
    description: 'Number of vehicle reservations successfully deleted',
    example: 2,
  })
  deleted: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully deleted 2 vehicle reservations',
  })
  message: string;

  @ApiProperty({
    description: 'Array of successfully deleted vehicle reservation IDs',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-426614174000',
      '123e4567-e89b-12d3-a456-426614174001',
    ],
  })
  deletedIds: string[];

  @ApiPropertyOptional({
    description: 'Array of failed deletions with error details',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'Vehicle reservation ID that failed to delete',
        },
        error: { type: 'string', description: 'Error message' },
      },
    },
  })
  failed?: Array<{
    id: string;
    error: string;
  }>;
}
