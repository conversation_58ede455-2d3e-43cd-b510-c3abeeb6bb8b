import { ApiProperty } from '@nestjs/swagger';

export class VehicleReservationNumberAvailabilityResponseDto {
  @ApiProperty({
    description: 'Whether the reservation number is available',
    example: true,
  })
  available: boolean;

  @ApiProperty({
    description: 'The reservation number that was checked',
    example: 'VR-2025-001',
  })
  reservationNumber: string;

  @ApiProperty({
    description: 'Message about availability',
    example: 'Reservation number is available',
  })
  message: string;
}
