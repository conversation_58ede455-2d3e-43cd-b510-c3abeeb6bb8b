import { ApiProperty } from '@nestjs/swagger';
import { VehicleReservationListDto } from './vehicle-reservation-list.dto';

export class PaginatedVehicleReservationsResponseDto {
  @ApiProperty({
    description: 'Array of vehicle reservations',
    type: [VehicleReservationListDto],
  })
  data: VehicleReservationListDto[];

  @ApiProperty({
    description: 'Total number of vehicle reservations',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 15,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
  })
  hasNextPage: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
  })
  hasPrevPage: boolean;
}
