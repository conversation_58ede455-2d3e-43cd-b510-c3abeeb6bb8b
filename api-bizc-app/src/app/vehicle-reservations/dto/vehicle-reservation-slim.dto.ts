import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  VehicleReservationStatus,
  PaymentStatus,
} from '../../shared/types/vehicle.enum';

export class VehicleReservationSlimDto {
  @ApiProperty({ description: 'Vehicle reservation ID' })
  id: string;

  @ApiProperty({ description: 'Reservation number' })
  reservationNumber: string;

  @ApiPropertyOptional({ description: 'External booking reference number' })
  referenceNumber?: string;

  @ApiPropertyOptional({ description: 'Customer name' })
  customerName?: string;

  @ApiProperty({ description: 'Pick up date and time' })
  pickUpDate: string;

  @ApiProperty({ description: 'Return date and time' })
  returnDate: string;

  @ApiProperty({
    enum: VehicleReservationStatus,
    description: 'Reservation status',
  })
  status: VehicleReservationStatus;

  @ApiProperty({ enum: PaymentStatus, description: 'Payment status' })
  paymentStatus: PaymentStatus;

  @ApiProperty({ description: 'Total amount' })
  total: string;

  @ApiProperty({ description: 'Number of vehicles in reservation' })
  vehicleCount: number;

  @ApiProperty({ description: 'Created at timestamp' })
  createdAt: string;
}
