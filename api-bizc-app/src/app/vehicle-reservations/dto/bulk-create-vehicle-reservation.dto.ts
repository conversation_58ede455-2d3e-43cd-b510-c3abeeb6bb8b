import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ValidateNested } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { CreateVehicleReservationDto } from './create-vehicle-reservation.dto';

export class BulkCreateVehicleReservationDto {
  @ApiProperty({
    description: 'Array of vehicle reservations to create',
    type: [CreateVehicleReservationDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateVehicleReservationDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  })
  vehicleReservations: CreateVehicleReservationDto[];
}
