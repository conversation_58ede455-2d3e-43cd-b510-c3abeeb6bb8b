import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, IsEnum } from 'class-validator';
import { VehicleReservationStatus } from '../../shared/types/vehicle.enum';

export class BulkUpdateVehicleReservationStatusDto {
  @ApiProperty({
    description: 'Array of vehicle reservation IDs to update',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  vehicleReservationIds: string[];

  @ApiProperty({
    enum: VehicleReservationStatus,
    description: 'New status for the vehicle reservations',
    example: VehicleReservationStatus.CONFIRMED,
  })
  @IsEnum(VehicleReservationStatus)
  status: VehicleReservationStatus;
}

export class BulkUpdateVehicleReservationStatusResponseDto {
  @ApiProperty({
    description: 'Number of vehicle reservations successfully updated',
    example: 2,
  })
  updated: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully updated status for 2 vehicle reservations',
  })
  message: string;

  @ApiProperty({
    description: 'Array of successfully updated vehicle reservation IDs',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  updatedIds: string[];

  @ApiProperty({
    description: 'Array of failed updates with error details',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'Vehicle reservation ID that failed to update',
        },
        error: { type: 'string', description: 'Error message' },
      },
    },
    required: false,
  })
  failed?: Array<{
    id: string;
    error: string;
  }>;
}
