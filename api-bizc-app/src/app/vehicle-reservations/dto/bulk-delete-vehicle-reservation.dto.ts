import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class BulkDeleteVehicleReservationDto {
  @ApiProperty({
    description: 'Array of vehicle reservation IDs to delete',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  vehicleReservationIds: string[];
}
