import { Module } from '@nestjs/common';
import { VehicleReservationsService } from './vehicle-reservations.service';
import { VehicleReservationsController } from './vehicle-reservations.controller';
import { AuthModule } from '../auth/auth.module';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { ActivityLogModule } from '../activity-log/activity-log.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [AuthModule, DrizzleModule, ActivityLogModule, UsersModule],
  controllers: [VehicleReservationsController],
  providers: [VehicleReservationsService],
  exports: [VehicleReservationsService],
})
export class VehicleReservationsModule {}
