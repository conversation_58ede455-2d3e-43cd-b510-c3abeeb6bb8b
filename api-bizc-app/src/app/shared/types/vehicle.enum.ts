/**
 * Vehicle-related enums used across the application
 */

/**
 * Vehicle Status
 * Status of a vehicle in the system
 */
export enum VehicleStatus {
  AVAILABLE = 'available',
  DIRTY = 'dirty',
  RETURNED = 'returned',
  OUT_OF_SERVICE = 'out_of_service',
  ON_SALE = 'on_sale',
  SOLD = 'sold',
  COMPLEMENTARY = 'complementary',
  NEW = 'new',
  RESERVED = 'reserved',
  STOLEN = 'stolen',
  RECOVERED = 'recovered',
  TOTALED = 'totaled',
  REPOSSESSED = 'repossessed',
  INACTIVE = 'inactive',
  DELETED = 'deleted',
}

/**
 * Vehicle Reservation Status
 * Status of vehicle reservations throughout their lifecycle
 */
export enum VehicleReservationStatus {
  INQUIRY = 'INQUIRY', // Initial inquiry, quotation stage
  PENDING = 'PENDING', // Reservation submitted, awaiting confirmation
  CONFIRMED = 'CONFIRMED', // Reservation confirmed, awaiting pickup
  PICKED_UP = 'PICKED_UP', // Vehicle picked up by customer
  RETURNED = 'RETURNED', // Vehicle returned by customer
  CANCELLED = 'CANCELLED', // Reservation cancelled
  NO_SHOW = 'NO_SHOW', // Customer failed to show up
  BLOCKED = 'BLOCKED', // Vehicle blocked for maintenance or other reasons
}

/**
 * Vehicle Pickup Location Type
 * Type of location for vehicle pickup/return
 */
export enum VehiclePickupLocationType {
  OFFICE = 'OFFICE', // Pick up from office location
  AIRPORT = 'AIRPORT', // Pick up from airport with flight details
  ADDRESS = 'ADDRESS', // Pick up from custom address
}
